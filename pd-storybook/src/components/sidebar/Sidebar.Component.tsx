import { useMemo, useState } from 'react';

import { cn } from '../../utils/Cn.Util';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { IconName } from '../iconImporter/IconMap.Component';
import { RouteLink } from '../routeLink/RouteLink.Component';

import {
  LogoutButton,
  ProfileUserInfo,
  UserMenuItem, UserSection,
} from './UserSection.Component';

// Tipos para los elementos del sidebar
interface SidebarSubItem {
  id: string;
  label: string;
  to: string;
}

interface SidebarItem {
  id: string;
  label: string;
  icon: IconName;
  onClick?: () => void;
  to?: string;
  subItems?: SidebarSubItem[];
}

interface SidebarGroup {
  groupLabel: string;
  items: SidebarItem[];
}

interface SidebarProps {
  className?: string;
  headerSection?: React.ReactNode;
  sidebarGroups: SidebarGroup[];
  path: string;
  profileUserInfo?: ProfileUserInfo;
  logoutButton?: LogoutButton;
  LinkComponent?: React.ComponentType<{ to: string; children: React.ReactNode; className?: string }>;
  userMenuItems?: UserMenuItem[];
}

export const Sidebar = ({
  className,
  headerSection,
  sidebarGroups,
  path,
  profileUserInfo,
  logoutButton,
  LinkComponent,
  userMenuItems,
}: SidebarProps) => {
  const activeItem = useMemo(() => {
    const allItems = sidebarGroups?.flatMap((group) => group.items || [])
      .flatMap((item) => (item?.subItems ? [item, ...item.subItems] : [item]))
      .filter((item) => item?.to) || [];

    const matchingItems = allItems.filter((item) => path.startsWith(item.to!));

    matchingItems.sort((a, b) => b.to!.length - a.to!.length);

    return matchingItems[0];
  }, [path, sidebarGroups]);

  const [open, setOpen] = useState<string | null>(() => {
    if (!activeItem) return null;

    const parentItem = sidebarGroups
      ?.flatMap((g) => g.items || [])
      .find((item) => item?.subItems?.some((sub) => sub.id === activeItem.id));

    return parentItem?.id || null;
  });

  const isItemActive = (item: SidebarItem) => {
    if (!activeItem || !item.to) return false;
    // Un item está activo si la ruta del item más específico (activeItem) empieza con la ruta de este item.
    return activeItem.to!.startsWith(item.to);
  };

  const isSubItemActive = (sub: SidebarSubItem) => {
    if (!activeItem || !sub.to) return false;
    // Un subitem está activo si es el item más específico.
    return activeItem.id === sub.id && activeItem.to === sub.to;
  };

  const getSubItemClass = (sub: SidebarSubItem) => {
    if (isSubItemActive(sub)) return 'pd-bg-gray-200 pd-text-primary pd-font-bold';
    return 'pd-text-gray-600 hover:pd-bg-gray-200';
  };

  const getItemClass = (item: SidebarItem) => {
    if (isItemActive(item)) return 'pd-bg-gray-200 pd-text-primary pd-font-bold';
    return 'pd-text-gray-700 hover:pd-bg-gray-200';
  };

  const renderSidebarItem = (item: SidebarItem) => {
    if (item.subItems) {
      return (
        <>
          <button
            type="button"
            className={[
              'pd-flex pd-w-full pd-items-center pd-gap-3 pd-rounded-md pd-px-3 pd-py-2 pd-text-sm pd-font-medium pd-transition pd-group',
              getItemClass(item),
            ].join(' ')}
            onClick={() => setOpen(open === item.id ? null : item.id)}
            aria-expanded={open === item.id}
          >
            <IconImporter name={item.icon} />
            {item.label}
            <IconImporter name="caretDown" className={`pd-transition-transform pd-ml-auto ${open === item.id ? 'pd-rotate-180' : ''}`} />
          </button>
          {item.subItems?.length && open === item.id && (
            <ul className="pd-ml-8 pd-mt-1 pd-space-y-1">
              {item.subItems?.map((sub: SidebarSubItem) => (
                <li key={sub.id}>
                  {LinkComponent ? (
                    <LinkComponent
                      to={sub.to}
                      className={`pd-block pd-px-2 pd-py-1 pd-text-xs pd-rounded ${getSubItemClass(sub)}`}
                    >
                      {sub.label}
                    </LinkComponent>
                  ) : (
                    <RouteLink
                      to={sub.to}
                      className={`pd-block pd-px-2 pd-py-1 pd-text-xs pd-rounded ${getSubItemClass(sub)}`}
                      style={{ fontSize: '12px' }}
                    >
                      {sub.label}
                    </RouteLink>
                  )}
                </li>
              ))}
            </ul>
          )}
        </>
      );
    }
    if (item.to && !item.subItems) {
      return LinkComponent ? (
        <LinkComponent
          to={item.to}
          className={`pd-flex pd-w-full pd-items-center pd-gap-3 pd-rounded-md pd-px-3 pd-py-2 pd-text-sm pd-font-medium pd-transition pd-group ${isItemActive(item)
            ? 'pd-bg-gray-200 pd-text-gray-700 pd-font-bold'
            : 'pd-text-gray-700 hover:pd-bg-gray-200'}`}
        >
          <IconImporter name={item.icon} />
          {item.label}
        </LinkComponent>
      ) : (
        <RouteLink
          to={item.to}
          className={`pd-flex pd-w-full pd-items-center pd-gap-3 pd-rounded-md pd-px-3 pd-py-2 pd-text-sm pd-font-medium pd-transition pd-group ${isItemActive(item)
            ? 'pd-bg-gray-200 pd-text-gray-700 pd-font-bold'
            : 'pd-text-gray-700 hover:pd-bg-gray-200'}`}
        >
          <IconImporter name={item.icon} />
          {item.label}
        </RouteLink>
      );
    }
    // Caso por defecto: botón
    return (
      <button
        className={[
          'pd-flex pd-w-full pd-items-center pd-gap-3 pd-rounded-md pd-px-3 pd-py-2',
          'pd-text-sm pd-font-medium pd-text-gray-700 hover:pd-bg-gray-200 pd-transition pd-group',
        ].join(' ')}
        onClick={() => {
          if (item.subItems) {
            setOpen(open === item.id ? null : item.id);
          }
          if (item.onClick) {
            item.onClick();
          }
        }}
      >
        <IconImporter name={item.icon} />
        {item.label}
        {item.subItems ? (
          <IconImporter
            name="caretDown"
            className={`pd-transition-transform pd-ml-auto ${open === item.id ? 'pd-rotate-180' : ''}`}
          />
        ) : null}
      </button>
    );
  };

  return (
    <aside
      className={cn(
        'pd-fixed pd-inset-y-0 pd-left-0 pd-z-20 pd-flex pd-h-full pd-w-[280px] pd-flex-col pd-px-4 pd-py-4 pd-shadow-sm',
        className,
      )}
      >
      {/* Header (customizable por prop) */}
      {headerSection ?? null}

      {/* Sidebar groups */}
      {sidebarGroups?.map((group) => (
        <div key={group.groupLabel}>
          <div className="pd-text-xs pd-text-gray-500 pd-font-semibold pd-px-2 pd-pt-2 pd-pb-1">{group.groupLabel}</div>
          <nav className="pd-flex-1">
            <ul className="pd-space-y-1">
              {group.items?.map((item: SidebarItem) => (
                <li key={item.id}>{renderSidebarItem(item)}</li>
              ))}
            </ul>
          </nav>
        </div>
      ))}
      {/* User section */}
      <UserSection profileUserInfo={profileUserInfo} logoutButton={logoutButton} items={userMenuItems} />

    </aside>
  );
};
