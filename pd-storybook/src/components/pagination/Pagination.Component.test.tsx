import { fireEvent, render, screen } from '@testing-library/react';

import { Pagination } from './Pagination.Component';

describe('Pagination Component', () => {
  const mockOnPageChange = jest.fn();

  const defaultProps = {
    totalPages: 10,
    onPageChange: mockOnPageChange,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders initial state correctly', () => {
      render(<Pagination {...defaultProps} />);

      expect(screen.getByRole('spinbutton')).toHaveValue(1);
      expect(screen.getByText('1 de 10')).toBeInTheDocument();
      expect(screen.getAllByRole('button')).toHaveLength(4);
    });

    it('renders with custom initial page', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);

      expect(screen.getByRole('spinbutton')).toHaveValue(5);
      expect(screen.getByText('5 de 10')).toBeInTheDocument();
    });
  });

  describe('Navigation Controls', () => {
    it('handles next page click', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);

      fireEvent.click(screen.getByTestId('caretRight'));
      expect(mockOnPageChange).toHaveBeenCalledWith(6);
    });

    it('handles previous page click', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);

      fireEvent.click(screen.getByTestId('caretLeft'));
      expect(mockOnPageChange).toHaveBeenCalledWith(4);
    });

    it('handles first page navigation', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);

      fireEvent.click(screen.getByTestId('caretLineLeft'));
      expect(mockOnPageChange).toHaveBeenCalledWith(1);
    });

    it('handles last page navigation', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);

      fireEvent.click(screen.getByTestId('caretLineRight'));
      expect(mockOnPageChange).toHaveBeenCalledWith(10);
    });
  });

  describe('Input Handling', () => {
    it('updates page on valid input change and blur', () => {
      render(<Pagination {...defaultProps} />);
      const input = screen.getByRole('spinbutton');

      fireEvent.change(input, { target: { value: '5' } });
      fireEvent.blur(input);

      expect(mockOnPageChange).toHaveBeenCalledWith(5);
    });

    it('updates page on Enter key press', () => {
      render(<Pagination {...defaultProps} />);
      const input = screen.getByRole('spinbutton');

      fireEvent.change(input, { target: { value: '7' } });
      fireEvent.keyDown(input, { key: 'Enter' });

      expect(mockOnPageChange).toHaveBeenCalledWith(7);
    });

    it('corrects values exceeding total pages', () => {
      render(<Pagination {...defaultProps} />);
      const input = screen.getByRole('spinbutton');

      fireEvent.change(input, { target: { value: '15' } });
      fireEvent.blur(input);

      expect(mockOnPageChange).toHaveBeenCalledWith(10);
    });

    it('corrects values below minimum page', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);
      const input = screen.getByRole('spinbutton');

      fireEvent.change(input, { target: { value: '0' } });
      fireEvent.blur(input);

      expect(mockOnPageChange).toHaveBeenCalledWith(1);
    });

    it('resets input to current page on invalid input (blur)', () => {
      render(<Pagination {...defaultProps} initialPage={1} />);
      const input = screen.getByRole('spinbutton');

      // Test invalid input from initial page 1
      fireEvent.change(input, { target: { value: 'invalid' } });
      fireEvent.blur(input);

      expect(input).toHaveValue(1);
      expect(mockOnPageChange).not.toHaveBeenCalled();
    });

    it('resets input to current page on invalid input (Enter key)', () => {
      render(<Pagination {...defaultProps} initialPage={1} />);
      const input = screen.getByRole('spinbutton');

      // Test invalid input from initial page 1
      fireEvent.change(input, { target: { value: 'abc' } });
      fireEvent.keyDown(input, { key: 'Enter' });

      expect(input).toHaveValue(1);
      expect(mockOnPageChange).not.toHaveBeenCalled();
    });

    it('handles empty string input (converts to 0)', () => {
      render(<Pagination {...defaultProps} initialPage={2} />);
      const input = screen.getByRole('spinbutton');

      // Test empty string input - Number('') returns 0, which gets corrected to 1
      fireEvent.change(input, { target: { value: '' } });
      fireEvent.blur(input);

      expect(mockOnPageChange).toHaveBeenCalledWith(1);
    });

    it('handles decimal input', () => {
      render(<Pagination {...defaultProps} initialPage={2} />);
      const input = screen.getByRole('spinbutton');

      // Test decimal input - Number('3.7') returns 3.7
      fireEvent.change(input, { target: { value: '3.7' } });
      fireEvent.blur(input);

      expect(mockOnPageChange).toHaveBeenCalledWith(3.7);
    });

    it('ignores non-Enter key presses', () => {
      render(<Pagination {...defaultProps} />);
      const input = screen.getByRole('spinbutton');

      fireEvent.change(input, { target: { value: '5' } });
      fireEvent.keyDown(input, { key: 'Tab' });

      expect(mockOnPageChange).not.toHaveBeenCalled();
    });

    it('does not call onPageChange when page number is the same', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);
      const input = screen.getByRole('spinbutton');

      fireEvent.change(input, { target: { value: '5' } });
      fireEvent.blur(input);

      expect(mockOnPageChange).not.toHaveBeenCalled();
    });

    it('handles NaN input correctly', () => {
      render(<Pagination {...defaultProps} initialPage={1} />);
      const input = screen.getByRole('spinbutton');

      // Test with a value that produces NaN when converted to Number
      fireEvent.change(input, { target: { value: 'NaN' } });
      fireEvent.blur(input);

      expect(input).toHaveValue(1);
      expect(mockOnPageChange).not.toHaveBeenCalled();
    });

    it('handles space character input', () => {
      render(<Pagination {...defaultProps} initialPage={3} />);
      const input = screen.getByRole('spinbutton');

      // Test with space character - Number(' ') returns 0, not NaN
      fireEvent.change(input, { target: { value: ' ' } });
      fireEvent.blur(input);

      // Should navigate to page 1 (since 0 gets corrected to 1)
      expect(mockOnPageChange).toHaveBeenCalledWith(1);
    });

    it('handles mixed alphanumeric input', () => {
      render(<Pagination {...defaultProps} initialPage={1} />);
      const input = screen.getByRole('spinbutton');

      // Test with mixed input that should produce NaN
      fireEvent.change(input, { target: { value: '3abc' } });
      fireEvent.blur(input);

      // This should reset to current page since Number('3abc') is NaN
      expect(input).toHaveValue(1);
      expect(mockOnPageChange).not.toHaveBeenCalled();
    });
  });

  describe('Loading State', () => {
    it('disables all controls when loading', () => {
      render(<Pagination {...defaultProps} isLoading={true} />);

      const buttons = screen.getAllByRole('button');
      const input = screen.getByRole('spinbutton');

      buttons.forEach((button) => {
        expect(button).toBeDisabled();
      });
      expect(input).toBeDisabled();
    });
  });

  describe('Edge Cases', () => {
    it('handles totalPages of 0 with fallback to 1', () => {
      render(<Pagination {...defaultProps} totalPages={0} />);
      const input = screen.getByRole('spinbutton');

      expect(input).toHaveAttribute('max', '1');
    });

    it('handles undefined totalPages with fallback to 1', () => {
      // @ts-expect-error - Testing edge case with undefined totalPages
      render(<Pagination onPageChange={mockOnPageChange} totalPages={undefined} />);
      const input = screen.getByRole('spinbutton');

      expect(input).toHaveAttribute('max', '1');
    });

    it('handles single page scenario', () => {
      render(<Pagination {...defaultProps} totalPages={1} />);

      const firstPageButton = screen.getByTestId('first-page');
      const previousPageButton = screen.getByTestId('previous-page');
      const nextPageButton = screen.getByTestId('next-page');
      const lastPageButton = screen.getByTestId('last-page');

      expect(firstPageButton).toBeDisabled();
      expect(previousPageButton).toBeDisabled();
      expect(nextPageButton).toBeDisabled();
      expect(lastPageButton).toBeDisabled();
    });

    it('updates state when initialPage prop changes', () => {
      const { rerender } = render(<Pagination {...defaultProps} initialPage={1} />);

      expect(screen.getByRole('spinbutton')).toHaveValue(1);
      expect(screen.getByText('1 de 10')).toBeInTheDocument();

      rerender(<Pagination {...defaultProps} initialPage={5} />);

      expect(screen.getByRole('spinbutton')).toHaveValue(5);
      expect(screen.getByText('5 de 10')).toBeInTheDocument();
    });

    it('handles boundary navigation correctly', () => {
      render(<Pagination {...defaultProps} initialPage={1} totalPages={3} />);

      // At first page, first and previous should be disabled
      expect(screen.getByTestId('first-page')).toBeDisabled();
      expect(screen.getByTestId('previous-page')).toBeDisabled();
      expect(screen.getByTestId('next-page')).not.toBeDisabled();
      expect(screen.getByTestId('last-page')).not.toBeDisabled();

      // Navigate to last page
      fireEvent.click(screen.getByTestId('last-page'));

      // At last page, next and last should be disabled
      expect(screen.getByTestId('first-page')).not.toBeDisabled();
      expect(screen.getByTestId('previous-page')).not.toBeDisabled();
      expect(screen.getByTestId('next-page')).toBeDisabled();
      expect(screen.getByTestId('last-page')).toBeDisabled();
    });
  });
});
