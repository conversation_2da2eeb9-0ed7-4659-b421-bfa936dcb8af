import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

import {
  FormInputType, InputComponent, InputPrice, InputProps,
} from './Input.Component';

describe('InputComponent', () => {
  const defaultProps: InputProps = {
    name: 'test-input',
    inputType: FormInputType.Text,
    placeholder: 'Enter text',
    label: 'Test Label',
    onChange: jest.fn(),
  };

  it('should render input with default props', () => {
    render(<InputComponent {...defaultProps} />);
    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveAttribute('type', 'text');
  });

  it('should render input with custom className', () => {
    render(<InputComponent {...defaultProps} inputClassName="custom-input-class" />);
    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toHaveClass('custom-input-class');
  });

  it('should call onChange when input value changes', () => {
    const handleChange = jest.fn();
    render(<InputComponent {...defaultProps} onChange={handleChange} />);
    const inputElement = screen.getByPlaceholderText('Enter text');
    fireEvent.change(inputElement, { target: { value: 'new value' } });
    expect(handleChange).toHaveBeenCalled();
  });

  it('should render input with value', () => {
    render(<InputComponent {...defaultProps} value="test value" />);
    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toHaveValue('test value');
  });

  it('should render input with name', () => {
    render(<InputComponent {...defaultProps} name="test-name" />);
    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toHaveAttribute('name', 'test-name');
  });

  it('should render input with placeholder', () => {
    render(<InputComponent {...defaultProps} placeholder="test placeholder" />);
    const inputElement = screen.getByPlaceholderText('test placeholder');
    expect(inputElement).toBeInTheDocument();
  });
});

describe('InputPrice', () => {
  const defaultProps = {
    onChange: jest.fn(),
    value: 100,
  };

  it('should render input with default props', () => {
    render(<InputPrice {...defaultProps} />);
    const inputElement = screen.getByDisplayValue('$100');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveClass('pd-outline-positive');
  });

  it('should render input with empty value when no value is provided', () => {
    render(<InputPrice onChange={jest.fn()} />);
    const inputElement = screen.getByDisplayValue('$0');
    expect(inputElement).toBeInTheDocument();
  });

  it('should render input as disabled', () => {
    render(<InputPrice {...defaultProps} disabled={true} />);
    const inputElement = screen.getByDisplayValue('$100');
    expect(inputElement).toBeDisabled();
  });

  it('renders with correct props', () => {
    render(
      <InputComponent
        name="testInput"
        inputType={FormInputType.Text}
        placeholder="Enter text"
      />,
    );

    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveAttribute('type', 'text');
    expect(inputElement).toHaveAttribute('name', 'testInput');
  });

  it('should call onChange with correct event when onValueChange is triggered', () => {
    const handleChange = jest.fn();
    render(<InputPrice onChange={handleChange} name='price' value={0}/>);

    const currencyInput = screen.getByTestId('input-price');

    fireEvent.change(currencyInput, { target: { value: '123' } });

    expect(handleChange).toHaveBeenCalled();
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          value: '123',
        }),
      }),
    );
  });

  it('should handle undefined localValue in inputChange function', () => {
    const handleChange = jest.fn();
    render(<InputPrice onChange={handleChange} name='price' value={0}/>);

    const currencyInput = screen.getByTestId('input-price');

    // Simulate onValueChange being called with undefined value
    fireEvent.change(currencyInput, { target: { value: '' } });

    expect(handleChange).toHaveBeenCalled();
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          value: '',
        }),
      }),
    );
  });

  it('should not call onChange when localName is undefined', () => {
    const handleChange = jest.fn();
    render(<InputPrice onChange={handleChange} value={0}/>); // No name prop

    const currencyInput = screen.getByTestId('input-price');

    fireEvent.change(currencyInput, { target: { value: '123' } });

    // onChange should not be called when name is undefined
    expect(handleChange).not.toHaveBeenCalled();
  });

  it('should render InputPrice with custom className', () => {
    render(<InputPrice className="custom-price-class" value={100} />);
    const inputElement = screen.getByDisplayValue('$100');
    expect(inputElement).toHaveClass('custom-price-class');
  });

  it('should handle undefined value in onValueChange callback', () => {
    const handleChange = jest.fn();

    // Create a mock component to test the inputChange function directly
    const TestComponent = () => {
      const inputChange = (localValue: string | undefined, localName?: string) => {
        if (localName) {
          const sanitizedValue = localValue ? localValue.replace(/[^\d.,]/g, '') : '';
          const event = {
            target: {
              name: localName,
              value: sanitizedValue,
            },
          } as React.ChangeEvent<HTMLInputElement>;

          if (handleChange) {
            handleChange(event);
          }
        }
      };

      // Test both branches: when localValue is undefined and when it has a value
      React.useEffect(() => {
        inputChange(undefined, 'test-name'); // This should trigger the empty string branch
        inputChange('$123.45abc', 'test-name'); // This should trigger the sanitization branch
      }, []);

      return <div data-testid="test-component" />;
    };

    render(<TestComponent />);

    expect(handleChange).toHaveBeenCalledTimes(2);

    // First call with undefined value should result in empty string
    expect(handleChange).toHaveBeenNthCalledWith(
      1,
      expect.objectContaining({
        target: expect.objectContaining({
          value: '',
        }),
      }),
    );

    // Second call with value should result in sanitized string
    expect(handleChange).toHaveBeenNthCalledWith(
      2,
      expect.objectContaining({
        target: expect.objectContaining({
          value: '123.45',
        }),
      }),
    );
  });
});

describe('InputComponent - Additional Coverage', () => {
  it('should use default inputType when not provided', () => {
    render(
      <InputComponent
        name="testInput"
        placeholder="Enter text"
      />,
    );

    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toHaveAttribute('type', 'text'); // Default FormInputType.Text
  });

  it('should render with different input types', () => {
    const { rerender } = render(
      <InputComponent
        name="testInput"
        inputType={FormInputType.Password}
        placeholder="Enter password"
      />,
    );

    let inputElement = screen.getByPlaceholderText('Enter password');
    expect(inputElement).toHaveAttribute('type', 'password');

    rerender(
      <InputComponent
        name="testInput"
        inputType={FormInputType.Email}
        placeholder="Enter email"
      />,
    );

    inputElement = screen.getByPlaceholderText('Enter email');
    expect(inputElement).toHaveAttribute('type', 'email');

    rerender(
      <InputComponent
        name="testInput"
        inputType={FormInputType.Number}
        placeholder="Enter number"
      />,
    );

    inputElement = screen.getByPlaceholderText('Enter number');
    expect(inputElement).toHaveAttribute('type', 'number');
  });

  it('should render with error state', () => {
    render(
      <InputComponent
        name="testInput"
        placeholder="Enter text"
        error={true}
      />,
    );

    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toBeInTheDocument();
  });

  it('should render with disabled state', () => {
    render(
      <InputComponent
        name="testInput"
        placeholder="Enter text"
        disabled={true}
      />,
    );

    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toBeDisabled();
  });

  it('should render with required attribute', () => {
    render(
      <InputComponent
        name="testInput"
        placeholder="Enter text"
        isRequired={true}
      />,
    );

    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toBeRequired();
  });

  it('should render with min and max attributes', () => {
    render(
      <InputComponent
        name="testInput"
        inputType={FormInputType.Number}
        placeholder="Enter number"
        min={1}
        max={100}
      />,
    );

    const inputElement = screen.getByPlaceholderText('Enter number');
    expect(inputElement).toHaveAttribute('min', '1');
    expect(inputElement).toHaveAttribute('max', '100');
  });

  it('should render with label and labelClassName', () => {
    render(
      <InputComponent
        name="testInput"
        placeholder="Enter text"
        label="Test Label"
        labelClassName="custom-label-class"
      />,
    );

    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toBeInTheDocument();
  });
});
