import { AddNewItemButton } from '#appComponent/common/AddNewItemButton.Component';
import { UsersListModule } from '#application/user/UsersList.Module';
import ApplicationRegistry from '#composition/Application.Registry';

export function UsersListLayout() {
  const addUserPath = `${ApplicationRegistry.PathService.user.base()}/new`;

  return (
    <div className='flex flex-col w-full pl-7 gap-4 pt-0'>
      <AddNewItemButton label='Agregar Usuario' path={addUserPath} className='self-end'/>
      <UsersListModule />
    </div>
  );
}
