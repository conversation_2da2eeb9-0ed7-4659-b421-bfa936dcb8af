import {
  Discount,
  NotificationComponent,
  ShoppingCart,
  ShoppingCartSummaryProps,
  Sidebar,
  SortAndMergeDiscounts,
} from '@pitsdepot/storybook';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import {
  useContext, useEffect, useState,
} from 'react';
import {
  Link, Outlet,
  useLocation, useNavigate,
} from 'react-router-dom';
import { ToastContainer } from 'react-toastify';

import suplifLogo from '#/assets/suplifai-logo.png';
import TextService from '#composition/textService/Text.Service';
import { AuthContext } from '#infrastructure/AuthState.Context';
import { GetEnv } from '#infrastructure/config/Enviroment.Config';
import CorePath from '#infrastructure/implementation/application/common/CorePath.Service';
import { CartContext } from '#infrastructure/pages/app/pageContexts/CartState.Context';
import 'dayjs/locale/es-mx';

dayjs.locale('es-mx');
dayjs.extend(utc);
dayjs.extend(timezone);

const { GENIA_WSP_NUMBER } = GetEnv();

const WSPMESSAGE = 'Hola, necesito ayuda con mi cuenta de Genia.';

const textService = TextService.getText();

export const HEADER = {
  profileUserSubmenu: [
    {
      id: '1',
      to: `https://api.whatsapp.com/send?phone=+${GENIA_WSP_NUMBER}&text=${WSPMESSAGE}`,
      children: 'Ayuda',
      target: '_blank',
    },
  ],
};

const {
  catalog, catalogDiscounts, claudia, clients, inventory, store, providers, storeDiscounts, saleOrders, purchaseOrders, user,
} = CorePath;

const sidebarGroups = [
  {
    groupLabel: 'Espacio de trabajo',
    items: [
      {
        id: 'store',
        label: 'Tienda',
        icon: 'storefront' as const,
        to: store.base(),
      },
      {
        id: 'claudia',
        label: 'Claudia',
        icon: 'brain' as const,
        to: claudia.base(),
      },
      {
        id: 'inventory',
        label: 'Inventario',
        icon: 'stack' as const,
        to: inventory.base(),
      },
      {
        id: 'catalog',
        label: 'Catálogo',
        icon: 'package' as const,
        to: catalog.base(),
      },
      {
        id: 'clients',
        label: 'Clientes',
        icon: 'addressBook' as const,
        to: clients.base(),
      },
      {
        id: 'providers',
        label: 'Proveedores',
        icon: 'factory' as const,
        subItems: [
          { id: 'my-providers', label: 'Mis Proveedores', to: providers.base() },
          { id: 'provider-catalog', label: 'Catálogo de Proveedores', to: providers.providerCatalogBase() },
        ],
      },
      {
        id: 'orders',
        label: 'Órdenes',
        icon: 'listChecks' as const,
        subItems: [
          { id: 'purchase-orders', label: 'Órdenes de Compra', to: saleOrders.base() },
          { id: 'sale-orders', label: 'Órdenes de Venta', to: purchaseOrders.base() },
        ],
      },
      {
        id: 'discounts',
        label: 'Descuentos',
        icon: 'tag' as const,
        subItems: [
          { id: 'catalog-discounts', label: 'Descuentos de Catálogo', to: catalogDiscounts.base() },
          { id: 'store-discounts', label: 'Descuentos de Tienda', to: storeDiscounts.base() },
        ],
      },
    ],
  },
  {
    groupLabel: 'Configuración',
    items: [
      {
        id: 'users',
        label: 'Usuarios',
        icon: 'users' as const,
        to: user.base(),
      },
    ],
  },
];

const HeaderSection = () => (
  <div className="flex items-center justify-between gap-3 px-2 py-3 mb-2 rounded">
    <div className="flex items-center gap-2">
      <img src={suplifLogo} alt="Suplifai Logo" className="h-8 w-8 object-contain" />
      <div className="flex flex-col">
        <span className="text-base font-bold text-gray-900 leading-tight">Suplifai</span>
      </div>
    </div>

    <div className="flex items-center">
      <NotificationComponent
        notifications={[]}
      />
    </div>
  </div>
);

export function AppPage() {
  const { logOut, userInfo } = useContext(AuthContext);
  const name = userInfo?.name || 'Guest';
  const userPhoto = userInfo?.picture || '';
  const userRol = userInfo?.rol || '';
  const currentPath = useLocation().pathname;
  const { cartState, cartFunctions } = useContext(CartContext);
  const [width, setWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  const {
    items, summary: {
      total, subtotal, totalTaxes, totalDiscount, subtotalBeforeDiscount,
    }, isCartOpen, isLoading,
  } = cartState;
  const {
    toggleCart, removeItem, incrementQuantity, decreaseQuantity, changeQuantity,
  } = cartFunctions;

  useEffect(() => {
    const handleResize = () => setWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (items?.length === 0) {
      toggleCart(false);
    } else {
      toggleCart(true);
    }
  }, [items]);

  const mappedItems = items.map((item) => ({
    ...item,
    id: item.referenceId,
    onRemove: () => removeItem(item.referenceId),
    onIncrement: () => incrementQuantity(item.referenceId),
    onDecrement: () => decreaseQuantity(item.referenceId),
    onQuantityChange: changeQuantity,
    applicableDiscounts: SortAndMergeDiscounts(item?.applicableDiscounts),
    appliedDiscount: item?.appliedDiscount?.catalogDiscount || item?.appliedDiscount?.storeDiscount as Discount,
  }));

  const cartSummary: ShoppingCartSummaryProps = {
    checkoutButton: {
      children: 'Crear orden',
      onClick: () => navigate(`${purchaseOrders.addPurchaseOrder()}/fromCart`),
    },
    total,
    subTotal: subtotal,
    taxesValue: totalTaxes,
    discountValue: totalDiscount,
    subtotalBeforeDiscount,
  };

  const [profileUserInfo, setProfileUserInfo] = useState({ userName: '', userProfilePhoto: '', userRol: '' });

  useEffect(() => {
    setProfileUserInfo({
      ...profileUserInfo, userName: name, userProfilePhoto: userPhoto, userRol,
    });
  }, [name, userPhoto, userRol]);

  let sidebarWidth = '0px';
  if (width <= 2145) {
    sidebarWidth = isCartOpen ? '256px' : '0px';
  }

  return (
    <>
      <ToastContainer/>

      <Sidebar
        sidebarGroups={sidebarGroups}
        path={currentPath}
        LinkComponent={Link}
        profileUserInfo={profileUserInfo}
        logoutButton={{
          onClick: logOut,
          children: textService.common.logout,
        }}
        userMenuItems={[
          {
            id: '1',
            label: 'Ayuda',
            icon: 'question' as const,
            onClick: () => window.open(
              `https://api.whatsapp.com/send?phone=+${GENIA_WSP_NUMBER}&text=${WSPMESSAGE}`,
              '_blank',
            ),
          },
        ]}
        headerSection={<HeaderSection />}
      />

      <div className='mi-h-screen flex pl-4 pr-4' style={{ marginLeft: 256 }}>
        <div style={{ width: currentPath === store.base(false) ? `calc(100% - ${sidebarWidth})` : '100%' }}>
          <div className='max-w-[1660px] mx-auto flex flex-col h-full'>
            <div className='flex h-full pr-[12px] pb-[2rem] w-full bg-dark-200'>
              <div className='overflow-hidden w-full mt-8'>
                <Outlet />
              </div>
            </div>
          </div>
        </div>
        {currentPath === store.base()
      && <div className='desktop:absolute' style={{ width: sidebarWidth }}>
        <ShoppingCart
          items={mappedItems}
          cartSummary={cartSummary}
          isOpen={isCartOpen}
          isLoading={isLoading}
        />
      </div>
        }
      </div>
    </>
  );
}
