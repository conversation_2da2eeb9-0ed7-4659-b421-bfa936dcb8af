import { gql, useQuery } from '@apollo/client';

import { GetTableDataProps } from '#appComponent/table/Table.Type';

const UsersQuery = gql`
  query GetUsers ($limit: Int, $offset: Int, $where: user_bool_exp) {
    user(
      limit: $limit,
      offset: $offset,
      where: $where
    ) {
      id
      email
      name
      lastName
      appPhoneNumber
      userCompanies {
        company {
          id
          name
        }
        role
      }
    }
    user_aggregate {
      aggregate {
        count
      }
    }
  }
`;

const useGetUsers = ({
  limit = 10, offset = 0, searchTerm,
}: GetTableDataProps) => {
  const searchCondition = searchTerm ? { email: { _ilike: `%${searchTerm}%` } } : {};
  const {
    data, loading, error, refetch,
  } = useQuery(UsersQuery, {
    variables: {
      limit,
      offset,
      where: searchCondition,
    },
  });

  return {
    items: data?.user,
    totalNumberOfItems: data?.user_aggregate?.aggregate?.count,
    loading,
    error: error as Error,
    refetch,
  };
};

const ReadModelUserOrderService = {
  useGetUsers,
};

export default ReadModelUserOrderService;
