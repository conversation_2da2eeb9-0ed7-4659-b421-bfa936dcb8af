import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.USER_COMPANY, async (table: Knex.CreateTableBuilder) => {
    table.enum('role', ['ADMIN', 'USER'], {
      useNative: true,
      enumName: 'user_company_role_enum',
    }).notNullable().defaultTo('USER');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TableNamesConfiguration.USER_COMPANY);

  await knex.raw('DROP TYPE IF EXISTS user_company_role_enum');
}
