import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import { seedClients } from '#infrastructure/databases/core/seeds/14_Client';
import { saleOrdersSeed } from '#infrastructure/databases/core/seeds/25_SaleOrders';
import { quotesSeed } from '#infrastructure/databases/core/seeds/31_Quote';
import { quoteProviderFieldsSeed } from '#infrastructure/databases/core/seeds/32_QuoteProviderFields';
import { quoteClientFieldsSeed } from '#infrastructure/databases/core/seeds/33_QuoteClientFields';

import { seedUsers } from './01_User';
import { seedCompanies } from './02_Company';
import { seedInventoryMedia } from './05_InventoryMedia';
import { seedTax } from './06_Tax';
import { seedInventoryTax } from './07_TaxInventory';
import { seedInventoryHistory } from './08_InventoryHistory';
import { seedAttributes } from './09_Attribute';
import { seedEntityContacts } from './10_EntityContact';
import { seedCatalogs } from './13_Catalog';
import { seedInventoryCatalog } from './15_InventoryCatalog';
import { seedCatalogDiscount } from './16_CatalogDiscount';
import { seedStoreDiscount } from './17_StoreDiscount';
import { seedCatalogDiscountCatalog } from './18_CatalogDiscountCatalog';
import { seedStoreDiscountClient } from './19_StoreDiscountClient';
import { seedCatalogDiscountClient } from './20_CatalogDiscountClient';
import { seedProviders } from './21_Provider';
import { seedProviderInventory } from './22_ProviderInventory';

export async function seed(knex: Knex): Promise<void> {
  await knex.transaction(async (trx) => {
    await trx(TableNamesConfiguration.QUOTE_CLIENT_FIELDS).whereIn('id', quoteClientFieldsSeed.map(({ id }) => id)).del();
    await trx(TableNamesConfiguration.QUOTE_PROVIDER_FIELDS).whereIn('id', quoteProviderFieldsSeed.map(({ id }) => id)).del();
    await trx(TableNamesConfiguration.QUOTE_ITEM).whereIn('quote_id', quotesSeed.map(({ id }) => id)).del();
    await trx(TableNamesConfiguration.QUOTE).whereIn('id', quotesSeed.map(({ id }) => id)).del();
    await trx(TableNamesConfiguration.SALE_ORDER).update({ relatedPurchaseOrderId: null });
    await trx(TableNamesConfiguration.PURCHASE_ORDER_ITEM).del();
    await trx(TableNamesConfiguration.PURCHASE_ORDER).del();
    await trx(TableNamesConfiguration.SALE_ORDER_ITEM).del();
    await trx(TableNamesConfiguration.SALE_ORDER).delete();
    await trx(TableNamesConfiguration.PROVIDER_INVENTORY).whereIn('providerId', seedProviderInventory.map((providerInventory) => providerInventory.providerId)).del();
    await trx(TableNamesConfiguration.PROVIDER).whereIn('id', seedProviders.map((provider) => provider.id)).del();
    await trx(TableNamesConfiguration.CATALOG_DISCOUNT_CLIENT)
      .whereIn('client_id', seedCatalogDiscountClient.map((catalogDiscountClient) => catalogDiscountClient.clientId)).del();
    await trx(TableNamesConfiguration.STORE_DISCOUNT_CLIENT)
      .whereIn('client_id', seedStoreDiscountClient.map((storeDiscountClient) => storeDiscountClient.clientId)).del();
    await trx(TableNamesConfiguration.CATALOG_DISCOUNT_CATALOG)
      .whereIn('catalog_id', seedCatalogDiscountCatalog.map((catalogDiscountCatalog) => catalogDiscountCatalog.catalogId)).del();
    await trx(TableNamesConfiguration.STORE_DISCOUNT).whereIn('id', seedStoreDiscount.map((storeDiscount) => storeDiscount.id)).del();
    await trx(TableNamesConfiguration.CATALOG_DISCOUNT).whereIn('id', seedCatalogDiscount.map((catalogDiscount) => catalogDiscount.id)).del();
    await trx(TableNamesConfiguration.INVENTORY_CATALOG).whereIn('id', seedInventoryCatalog.map((inventoryCatalog) => inventoryCatalog.id)).del();
    await trx(TableNamesConfiguration.CATALOG_TAX).whereIn(['catalogId', 'taxId'], seedCatalogs.map((catalog) => [catalog.id, seedTax[0].id])).del();
    await trx(TableNamesConfiguration.SALE_ORDER_ITEM).del();
    await trx(TableNamesConfiguration.SALE_ORDER).whereIn('id', saleOrdersSeed.map((saleOrder) => saleOrder.id)).del();
    await trx(TableNamesConfiguration.CLIENT).whereIn('id', seedClients.map((client) => client.id)).del();
    await trx(TableNamesConfiguration.CATALOG).whereIn('companyId', seedCatalogs.map((catalog) => catalog.companyId)).del();
    await trx(TableNamesConfiguration.ENTITY_CONTACT).whereIn('entity_id', seedEntityContacts.map((contact) => contact.entity_id)).del();
    await trx(TableNamesConfiguration.ATTRIBUTE).whereIn('id', seedAttributes.map((attribute) => attribute.id)).del();
    await trx(TableNamesConfiguration.INVENTORY_HISTORY).whereIn('id', seedInventoryHistory.map((history) => history.id) as string[]).del();
    await trx(TableNamesConfiguration.INVENTORY_TAX).whereIn(['inventoryId', 'taxId'], seedInventoryTax.map((it) => [it.inventoryId, it.taxId])).del();
    await trx(TableNamesConfiguration.TAX).whereIn('id', seedTax.map((tax) => tax.id)).del();
    await trx(TableNamesConfiguration.INVENTORY_MEDIA).whereIn('id', seedInventoryMedia.map((media) => media.id)).del();
    await trx(TableNamesConfiguration.INVENTORY_INDEX).whereIn('companyId', seedCompanies.map((company) => company.id)).del();
    await trx(TableNamesConfiguration.INVENTORY).whereIn('companyId', seedCompanies.map((company) => company.id)).del();
    await trx(TableNamesConfiguration.USER_COMPANY).whereIn('companyId', seedCompanies.map((company) => company.id)).del();
    await trx(TableNamesConfiguration.COMPANY).whereIn('id', seedCompanies.map((company) => company.id)).del();
    await trx(TableNamesConfiguration.USER).whereIn('email', seedUsers.map((user) => user.email)).del();
  });
}
