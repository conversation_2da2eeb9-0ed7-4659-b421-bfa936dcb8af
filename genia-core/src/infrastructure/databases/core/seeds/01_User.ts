import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export const seedUsers = [{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea896',
  email: '<EMAIL>',
  name: '<PERSON>',
  lastName: 'Doe',
  createdAt: new Date(),
  updatedAt: new Date(),
  appPhoneNumber: '+1234567890',
},
{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea897',
  email: '<EMAIL>',
  name: '<PERSON>',
  lastName: '<PERSON><PERSON>',
  createdAt: new Date(),
  updatedAt: new Date(),
  appPhoneNumber: '+1234567891',
},
{
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea899',
  email: '<EMAIL>',
  name: '<PERSON><PERSON><PERSON>',
  lastName: '<PERSON><PERSON><PERSON>',
  createdAt: new Date(),
  updatedAt: new Date(),
  appPhoneNumber: '+1234567892',
},
];

export async function seed(knex: Knex): Promise<void> {
  await knex(TableNamesConfiguration.USER).insert(seedUsers);
}
