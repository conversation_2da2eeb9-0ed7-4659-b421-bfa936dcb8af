import { Knex } from 'knex';

import { UserRole } from '#domain/aggregates/user/User.Entity';
import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

import { seedUsers } from './01_User';
import { seedCompanies } from './02_Company';

export const seedUserCompanies = [
  {
    userId: seedUsers[0].id,
    companyId: seedCompanies[0].id,
    createdAt: new Date(),
    updatedAt: new Date(),
    role: UserRole.ADMIN,
  },
  {
    userId: seedUsers[1].id,
    companyId: seedCompanies[0].id,
    createdAt: new Date(),
    updatedAt: new Date(),
    role: UserRole.USER,
  },
  {
    userId: seedUsers[2].id,
    companyId: seedCompanies[5].id,
    createdAt: new Date(),
    updatedAt: new Date(),
    role: UserRole.ADMIN,
  },
];

export async function seed(knex: Knex): Promise<void> {
  await knex(TableNamesConfiguration.USER_COMPANY).insert(seedUserCompanies);
}
