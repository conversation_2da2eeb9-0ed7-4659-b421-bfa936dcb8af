import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export const seedCompanies = [
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea898',
    name: 'PitsDepot development',
    tributaryId: '20990222031',
    description: 'Automotive parts and services company',
    country: 'MX',
    verifiedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea897',
    name: 'Acme Company',
    tributaryId: '10880111020',
    description: 'A company known for its wide range of products and services',
    country: 'MX',
    verifiedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea896',
    name: 'Umbrella Corporation',
    tributaryId: '10880111021',
    country: 'CO',
    description: 'A multinational pharmaceutical and biotechnology company',
    verifiedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea895',
    name: 'Dharma Initiative',
    tributaryId: '***********',
    description: 'A scientific research project with a mysterious agenda',
    country: 'MX',
    verifiedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea894',
    name: 'LexCorp',
    tributaryId: '***********',
    description: 'A multinational corporation known for its advanced technology and business practices',
    country: 'CO',
    verifiedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea893',
    name: 'Planet Express',
    tributaryId: '***********',
    verifiedAt: new Date(),
    description: 'A space-age delivery service company',
    country: 'MX',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export async function seed(knex: Knex): Promise<void> {
  await knex(TableNamesConfiguration.COMPANY).insert(seedCompanies);
}
