import { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

import { seedCompanies } from './02_Company';

export const seedProviders = [
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eeabb1',
    name: 'Provider without company',
    tributaryId: '20990222031',
    providerCompanyId: null,
    companyId: seedCompanies[0].id,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eeabb2',
    name: 'Acme Company',
    tributaryId: null,
    providerCompanyId: seedCompanies[1].id,
    companyId: seedCompanies[0].id,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eeabb3',
    name: 'Umbrella Corporation',
    tributaryId: null,
    providerCompanyId: seedCompanies[2].id,
    companyId: seedCompanies[0].id,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eeabb4',
    name: 'Dharma Initiative',
    tributaryId: null,
    providerCompanyId: seedCompanies[3].id,
    companyId: seedCompanies[0].id,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eeabb5',
    name: 'Pits depot as planet express provider',
    tributaryId: null,
    providerCompanyId: seedCompanies[0].id,
    companyId: seedCompanies[5].id,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export async function seed(knex: Knex): Promise<void> {
  await knex(TableNamesConfiguration.PROVIDER).insert(seedProviders);
}
