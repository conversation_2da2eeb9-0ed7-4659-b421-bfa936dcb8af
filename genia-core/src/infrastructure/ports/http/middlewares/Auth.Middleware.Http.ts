import {
  NextFunction, Request,
  Response,
} from 'express';
import { auth } from 'express-oauth2-jwt-bearer';

import EnvConfiguration from '#infrastructure/configurations/Env.configuration';
import { uuidRegExp } from '#infrastructure/ports/http/RegExp';

import ErrorsHttp from '../Errors.Http';

const whiteListedRoutes = [
  '/users/register',
  /^\/media\/[^/]+$/,
  new RegExp(`/users/(${uuidRegExp})/claims`, 'i'),
];

const {
  AUTH0_AUDIENCE, AUTH0_DOMAIN, AUTH0_ALGORITHM, CLAIMS_NAMESPACE,
} = EnvConfiguration.getEnv();

const jwtCheck = auth({
  audience: AUTH0_AUDIENCE,
  issuerBaseURL: AUTH0_DOMAIN,
  tokenSigningAlg: AUTH0_ALGORITHM,
});

function validateAccess(req: Request, res: Response, next:NextFunction): void {
  const { headers: { authorization, 'log-as': logAs }, path } = req;

  const isWhitelisted = whiteListedRoutes.some((route) => (typeof route === 'string' ? route === path : route instanceof RegExp && route.test(path)));

  if (isWhitelisted) {
    req.authorization = {
      companyId: 'register',
      userId: 'register',
    };

    return next();
  }

  const devAsCompany = process.env.NODE_ENV === 'development' ? logAs as string || null : null;

  if (!authorization) throw new ErrorsHttp.Unauthorized();

  const customNext = (error?: Error): void => {
    if (error) return next(new ErrorsHttp.Unauthorized(error.name));

    const claims = req.auth?.payload[CLAIMS_NAMESPACE] as { 'company-id': string, 'user-id': string };
    req.authorization = {
      companyId: devAsCompany || claims['company-id'],
      userId: claims['user-id'],
    };

    return next();
  };

  try {
    return jwtCheck(req, res, customNext as NextFunction);
  } catch (error) {
    throw new ErrorsHttp.Unauthorized((error as Error).message);
  }
}

const AuthMiddlewareHttp = {
  validateAccess,
};

export default AuthMiddlewareHttp;
