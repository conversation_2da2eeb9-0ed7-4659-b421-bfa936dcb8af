// filepath: /Users/<USER>/Desktop/docsOscar/pitsDepot/pd-monorepo/genia-core/src/infrastructure/ports/http/resources/users/schemas/ClaimsResponse.Schema.ts
export interface ClaimsResponseSchemaShape {
  metadata: {
    claims: {
      [key: string]: {
        'user-id': string;
        'company-id': string;
      } | {
        'x-hasura-allowed-roles': string[];
        'x-hasura-default-role': string;
        'x-hasura-user-id': string;
        'x-hasura-company-id': string;
      }
    }
  };
}

const ClaimsResponseSchema = {
  type: 'object',
  required: ['metadata'],
  properties: {
    metadata: {
      type: 'object',
      required: ['claims'],
      properties: {
        claims: {
          type: 'object',
          additionalProperties: true,
        },
      },
      additionalProperties: false,
    },
  },
  additionalProperties: false,
};

export default ClaimsResponseSchema;
