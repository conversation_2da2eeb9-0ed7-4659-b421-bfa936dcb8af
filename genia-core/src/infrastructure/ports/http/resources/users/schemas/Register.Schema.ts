import { JSONSchemaType } from 'ajv';

export interface registerSchema {
  user: {
    email: string;
    name: string;
    lastName: string;
    phoneNumber: string;
  }
  company: {
    name: string;
    description?: string;
    tributaryId: string;
    country: string;
  };
}

const RegisterSchema: JSONSchemaType<registerSchema> = {
  type: 'object',
  required: ['user', 'company'],
  properties: {
    user: {
      type: 'object',
      properties: {
        email: { type: 'string', format: 'email' },
        name: { type: 'string' },
        lastName: { type: 'string' },
        phoneNumber: { type: 'string' },
      },
      required: ['email', 'phoneNumber', 'name', 'lastName'],
      additionalProperties: false,
    },
    company: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        description: { type: 'string', nullable: true },
        tributaryId: { type: 'string' },
        country: {
          type: 'string',
          enum: ['MX', 'CO', 'CL', 'BR', 'AR'],
        },
      },
      required: ['name', 'tributaryId', 'country'],
      additionalProperties: false,
    },
  },
  additionalProperties: false,
};

export default RegisterSchema;
