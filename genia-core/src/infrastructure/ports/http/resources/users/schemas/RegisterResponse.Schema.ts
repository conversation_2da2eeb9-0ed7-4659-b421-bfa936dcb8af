import { JSONSchemaType } from 'ajv';

export interface registerResponseSchema {
  user: {
    id: string;
    email: string;
    companies: string[];
  },
  company: {
    id: string;
    name: string;
  }
}

const RegisterResponseSchema: JSONSchemaType<registerResponseSchema> = {
  type: 'object',
  required: ['user', 'company'],
  properties: {
    user: {
      type: 'object',
      required: ['id', 'email', 'companies'],
      properties: {
        id: { type: 'string' },
        email: { type: 'string', format: 'email' },
        companies: { type: 'array', items: { type: 'string' } },
      },
      additionalProperties: false,
    },
    company: {
      type: 'object',
      required: ['id', 'name'],
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
      },
      additionalProperties: false,
    },
  },
  additionalProperties: false,
};

export default RegisterResponseSchema;
