import { Request, Response } from 'express';

import UserUseCase from '#application/user/useCases/User.UseCase';
import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import UserEntity, { UserRole } from '#domain/aggregates/user/User.Entity';
import EnvConfiguration from '#infrastructure/configurations/Env.configuration';
import UserController from '#infrastructure/ports/http/resources/users/controllers/User.Controller';
import RegisterSchema from '#infrastructure/ports/http/resources/users/schemas/Register.Schema';
import SchemasValidatorUtility from '#infrastructure/utilities/SchemasValidator.Utility';

jest.unmock('#infrastructure/ports/http/resources/users/controllers/User.Controller');
jest.unmock('#infrastructure/ports/http/resources/users/schemas/Register.Schema');
jest.unmock('#infrastructure/ports/http/Errors.Http');

const { CLAIMS_NAMESPACE, HASURA_DEFAULT_ROLE } = EnvConfiguration.getEnv();

describe('UserController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    mockRequest = {};
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis(),
    };
    jest.clearAllMocks();
  });

  describe('register', () => {
    it('should register a user successfully', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
        },
      };

      mockRequest.body = mockBody;

      const mockUser: UserEntity = {
        id: 'user-id-123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company-id-123'],
        role: UserRole.ADMIN,
      };

      const mockCompany: CompanyEntity = {
        id: 'company-id-123',
        name: 'Test Company',
        description: 'A test company',
        tributaryId: 'XAXX010101000',
        country: 'MX',
      };

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);
      jest.spyOn(UserUseCase, 'register').mockResolvedValue({ user: mockUser, company: mockCompany });

      await UserController.register(mockRequest as Request, mockResponse as Response);

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(UserUseCase.register).toHaveBeenCalledWith({
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
        },
      });
      expect(mockResponse.send).toHaveBeenCalledWith({
        user: mockUser,
        company: mockCompany,
      });
    });

    it('should return 400 if schema validation fails', async () => {
      const mockBody = {
        user: {
          email: 'invalid-email',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
        },
      };

      mockRequest.body = mockBody;

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => {
        throw new Error('Invalid schema');
      });

      await expect(UserController.register(mockRequest as Request, mockResponse as Response))
        .rejects.toThrow('Invalid schema');

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(mockResponse.send).not.toHaveBeenCalled();
    });

    it('should return 400 for invalid country', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: '12345678901',
          country: 'INVALID',
        },
      };

      mockRequest.body = mockBody;

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);

      await expect(UserController.register(mockRequest as Request, mockResponse as Response))
        .rejects.toThrow('Invalid country');

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(mockResponse.send).not.toHaveBeenCalled();
    });

    it('should return 400 for invalid tributary ID format', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'invalid-rfc',
          country: 'MX',
        },
      };

      mockRequest.body = mockBody;

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);

      await expect(UserController.register(mockRequest as Request, mockResponse as Response))
        .rejects.toThrow('Invalid tributary ID for country MX');

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(mockResponse.send).not.toHaveBeenCalled();
    });

    it('should return 409 if user already exists', async () => {
      const mockBody = {
        user: {
          email: '<EMAIL>',
          name: 'John',
          lastName: 'Doe',
          phoneNumber: '1234567890',
        },
        company: {
          name: 'Test Company',
          description: 'A test company',
          tributaryId: 'XAXX010101000',
          country: 'MX',
        },
      };

      mockRequest.body = mockBody;

      jest.spyOn(SchemasValidatorUtility, 'schemasValidation').mockImplementation(() => true);
      jest.spyOn(UserUseCase, 'register').mockRejectedValue(new Error('Email already exists'));

      await expect(UserController.register(mockRequest as Request, mockResponse as Response))
        .rejects.toThrow('Email already exists');

      expect(SchemasValidatorUtility.schemasValidation).toHaveBeenCalledWith(RegisterSchema, mockBody);
      expect(mockResponse.send).not.toHaveBeenCalled();
    });
  });

  describe('getClaims', () => {
    it('should return claims for a valid user', async () => {
      const userId = 'user-id-123';
      mockRequest.params = { userId };

      const mockUser: UserEntity = {
        id: userId,
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company-id-123'],
        role: UserRole.ADMIN,
      };

      jest.spyOn(UserUseCase, 'retrieveUser').mockResolvedValue(mockUser);

      await UserController.getClaims(mockRequest as Request<{userId: string}>, mockResponse as Response);

      expect(UserUseCase.retrieveUser).toHaveBeenCalledWith({ userId });
      expect(mockResponse.send).toHaveBeenCalledWith({
        metadata: {
          claims: {
            [CLAIMS_NAMESPACE]: {
              'user-id': userId,
              'company-id': 'company-id-123',
            },
            'https://hasura.io/jwt/claims': {
              'x-hasura-allowed-roles': [HASURA_DEFAULT_ROLE],
              'x-hasura-default-role': HASURA_DEFAULT_ROLE,
              'x-hasura-user-id': userId,
              'x-hasura-company-id': 'company-id-123',
            },
          },
        },
      });
    });

    it('should throw error if user is not found', async () => {
      const userId = 'non-existent-user';
      mockRequest.params = { userId };

      jest.spyOn(UserUseCase, 'retrieveUser').mockRejectedValue(new Error('User not found'));

      await expect(UserController.getClaims(mockRequest as Request<{userId: string}>, mockResponse as Response))
        .rejects.toThrow('User not found');

      expect(UserUseCase.retrieveUser).toHaveBeenCalledWith({ userId });
      expect(mockResponse.send).not.toHaveBeenCalled();
    });
  });
});
