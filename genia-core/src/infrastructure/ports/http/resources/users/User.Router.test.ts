import express from 'express';

import HandlersHttp from '#infrastructure/ports/http/Handlers.Http';
import ApiKeyMiddleWare from '#infrastructure/ports/http/middlewares/ApiKey.Middleware.Http';
import { uuidRegExp } from '#infrastructure/ports/http/RegExp';
import UserController from '#infrastructure/ports/http/resources/users/controllers/User.Controller';
import UsersRoutes from '#infrastructure/ports/http/resources/users/User.Router';

jest.unmock('#infrastructure/ports/http/resources/users/User.Router');

jest.mock('express', () => ({
  Router: jest.fn().mockReturnValue({}),
}));

jest.mock('#infrastructure/ports/http/Handlers.Http', () => ({
  handleAndCatch: jest.fn((c) => c),
}));

describe('UserRouter', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Set up mock methods on the express router
    express.Router().post = jest.fn();
    express.Router().get = jest.fn();
  });

  it('should set up all necessary routes', () => {
    UsersRoutes.getRoutes();

    // Verify all routes were set up
    expect(express.Router().post).toHaveBeenCalledTimes(1);
    expect(express.Router().get).toHaveBeenCalledTimes(1);
  });

  it('should correctly set up registration route', () => {
    UsersRoutes.getRoutes();

    expect(express.Router().post).toHaveBeenCalledWith(
      '/register',
      ApiKeyMiddleWare.validateAccess,
      HandlersHttp.handleAndCatch(UserController.register),
    );
  });

  it('should correctly set up claims route with UUID regex', () => {
    UsersRoutes.getRoutes();

    const claimsPath = `/:userId(${uuidRegExp})/claims`;
    expect(express.Router().get).toHaveBeenCalledWith(
      claimsPath,
      ApiKeyMiddleWare.validateAccess,
      HandlersHttp.handleAndCatch(UserController.getClaims),
    );
  });
});
