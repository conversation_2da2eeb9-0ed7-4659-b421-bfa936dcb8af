enum TableNamesConfiguration {
  ATTRIBUTE = 'attribute',
  CATALOG = 'catalog',
  CATALOG_DISCOUNT = 'catalog_discount',
  CATALOG_DISCOUNT_CATALOG = 'catalog_discount_catalog',
  CATALOG_DISCOUNT_CLIENT = 'catalog_discount_client',
  CATALOG_INDEX = 'catalog_index',
  CATALOG_TAX = 'catalog_tax',
  CLIENT = 'client',
  COMPANY = 'company',
  COMPANY_SEQUENCE = 'company_sequence',
  ENTITY_CONTACT = 'entity_contact',
  INVENTORY = 'inventory',
  INVENTORY_CATALOG = 'inventory_catalog',
  INVENTORY_HISTORY = 'inventory_history',
  INVENTORY_INDEX = 'inventory_index',
  INVENTORY_MEDIA = 'inventory_media',
  CATALOG_MEDIA = 'catalog_media',
  INVENTORY_TAX = 'inventory_tax',
  PROVIDER = 'provider',
  PROVIDER_INVENTORY = 'provider_inventory',
  STORE_DISCOUNT = 'store_discount',
  STORE_DISCOUNT_CLIENT = 'store_discount_client',
  TAX = 'tax',
  USER = 'user',
  USER_COMPANY = 'user_company',
  SALE_ORDER = 'sale_order',
  PURCHASE_ORDER = 'purchase_order',
  SALE_INVOICE = 'sale_invoice',
  PURCHASE_INVOICE = 'purchase_invoice',
  PROVIDER_PAYMENT = 'provider_payment',
  SALE_PAYMENT = 'sale_payment',
  SALE_ORDER_ITEM = 'sale_order_item',
  SALE_ORDER_ITEM_TAX = 'sale_order_item_tax',
  PURCHASE_ORDER_ITEM = 'purchase_order_item',
  QUOTE = 'quote',
  QUOTE_ITEM = 'quote_item',
  QUOTE_PROVIDER_FIELDS = 'quote_provider_fields',
  QUOTE_CLIENT_FIELDS = 'quote_client_fields',
  COUNTRY = 'country',
}

export default TableNamesConfiguration;
