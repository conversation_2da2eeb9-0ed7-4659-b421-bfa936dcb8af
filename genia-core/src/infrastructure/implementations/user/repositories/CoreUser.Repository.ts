import UserRepository, { CreateUser } from '#application/user/repositories/User.Repository';
import UserEntity from '#domain/aggregates/user/User.Entity';
import UserOperator from '#domain/aggregates/user/User.Operator';
import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import CoreDatabase from '#infrastructure/databases/core/Core.Database';
import { dbUser } from '#infrastructure/databases/core/types';

const { USER, USER_COMPANY } = TableNamesConfiguration;

const userSelect: Array<keyof dbUser> = ['id', 'email', 'name', 'lastName', 'appPhoneNumber'];

async function findCompaniesById(id: string): Promise<string[]> {
  const db = CoreDatabase.getClient();

  const companies = await db(USER_COMPANY).select('companyId').where({ userId: id });

  return companies.map((c) => c.companyId);
}

async function findOneByPhoneNumber(phoneNumber: string): Promise<UserEntity | undefined> {
  const db = CoreDatabase.getClient();
  const user = await db(USER).select(userSelect).where({ appPhoneNumber: phoneNumber }).first();

  if (!user) return undefined;

  const companies = await findCompaniesById(user.id);
  return UserOperator.build({ ...user, companies, phoneNumber: user.appPhoneNumber });
}

async function findOneByEmail(email: string): Promise<UserEntity | undefined> {
  const db = CoreDatabase.getClient();

  const user = await db(USER).select(userSelect).where({ email }).first();

  if (!user) return undefined;

  const companies = await findCompaniesById(user.id);

  return UserOperator.build({ ...user, companies, phoneNumber: user.appPhoneNumber });
}

async function createOne(createUser: CreateUser): Promise<Omit<UserEntity, 'password'>> {
  const db = CoreDatabase.getClient();

  const {
    companies, email, lastName, name, phoneNumber: appPhoneNumber, role,
  } = createUser;

  return db.transaction<Omit<UserEntity, 'password'>>(async (trx) => {
    const newUser = (await trx(USER).insert({
      email, lastName, name, appPhoneNumber,
    }).returning(userSelect))[0];

    const companiesToAdd = companies.map((companyId) => ({ userId: newUser.id, companyId, role }));

    await trx(USER_COMPANY).insert(companiesToAdd);

    return UserOperator.build({
      ...newUser, companies, phoneNumber: newUser.appPhoneNumber, role: companiesToAdd[0].role,
    });
  });
}

async function findOneById(id: string): Promise<UserEntity | undefined> {
  const db = CoreDatabase.getClient();

  const user = await db(USER).select(userSelect).where('id', id).first();

  if (!user) return undefined;

  const companies = await findCompaniesById(user.id);

  return UserOperator.build({ ...user, companies, phoneNumber: user.appPhoneNumber });
}

async function update(user: UserEntity): Promise<void> {
  const db = CoreDatabase.getClient();

  const {
    id, companies, phoneNumber, role, ...userData
  } = user;
  const dataToUpdate = { ...userData, appPhoneNumber: phoneNumber };

  await db.transaction(async (trx) => {
    await trx(USER).where('id', id).update(dataToUpdate);

    await trx(USER_COMPANY).where('userId', id).delete();

    await trx(USER_COMPANY).insert(companies.map((companyId) => ({ userId: id, companyId, role })));
  });
}

const CoreUserRepository: UserRepository = {
  findOneByEmail,
  createOne,
  findOneById,
  update,
  findOneByPhoneNumber,
};

export default CoreUserRepository;
