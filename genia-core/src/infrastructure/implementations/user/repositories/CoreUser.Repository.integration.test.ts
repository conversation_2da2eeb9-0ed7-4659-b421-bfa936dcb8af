import { v4 } from 'uuid';

import UserEntity, { UserRole } from '#domain/aggregates/user/User.Entity';
import CoreDatabase from '#infrastructure/databases/core/Core.Database';
import { seedUsers } from '#infrastructure/databases/core/seeds/01_User';
import { seedCompanies } from '#infrastructure/databases/core/seeds/02_Company';
import { dbCompany } from '#infrastructure/databases/core/types';
import DatabaseTesting from '#infrastructure/testing/Database.Testing';

import CoreUserRepository from './CoreUser.Repository';

jest.unmock('uuid');
jest.unmock('pg');

jest.unmock('./CoreUser.Repository');

jest.unmock('#domain/aggregates/user/User.Operator');
jest.unmock('#infrastructure/testing/Database.Testing');
jest.unmock('#infrastructure/databases/core/seeds/01_User');
jest.unmock('#infrastructure/databases/core/seeds/02_Company');
jest.unmock('#infrastructure/databases/core/seeds/03_UserCompany');

describe('CoreUserRepository Integration Test', () => {
  const SEED_USER = seedUsers[0];
  const SEED_COMPANY = seedCompanies[0];
  const testCompany: dbCompany = {
    id: v4(),
    name: 'Test Company',
    description: 'Test Description',
    country: 'US',
    tributaryId: '12345678',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    await DatabaseTesting.setUpDatabase();
    // Insert the test company
    await CoreDatabase.getClient()('company').insert(testCompany);
  });

  afterEach(async () => {
    await DatabaseTesting.tearDownDatabase();
  });

  describe('findOneById', () => {
    it('should return a user with companies', async () => {
      const user = await CoreUserRepository.findOneById(SEED_USER.id);

      expect(user).toBeDefined();
      expect(user?.companies).toContain(SEED_COMPANY.id);
    });

    it('should return undefined for non-existent user', async () => {
      const user = await CoreUserRepository.findOneById(v4());
      expect(user).toBeUndefined();
    });
  });

  describe('createOne', () => {
    it('should create a new user with company association', async () => {
      const newUser: Omit<UserEntity, 'id'> = {
        email: '<EMAIL>',
        name: 'Test',
        lastName: 'User',
        phoneNumber: '1234567890',
        companies: [SEED_COMPANY.id],
        role: UserRole.ADMIN,
      };

      const createdUser = await CoreUserRepository.createOne(newUser);

      expect(createdUser.email).toBe(newUser.email);
      expect(createdUser.name).toBe(newUser.name);
      expect(createdUser.lastName).toBe(newUser.lastName);
      expect(createdUser.phoneNumber).toBe(newUser.phoneNumber);
      expect(createdUser.companies).toEqual(newUser.companies);
      expect(createdUser.role).toBe(newUser.role);

      // Verify company association
      const userWithCompanies = await CoreUserRepository.findOneById(createdUser.id);
      expect(userWithCompanies?.companies).toContain(SEED_COMPANY.id);
    });
  });

  describe('update', () => {
    it('should update user and company associations', async () => {
      const user = await CoreUserRepository.findOneById(SEED_USER.id);
      expect(user).toBeDefined();

      if (user) {
        const updatedUser: UserEntity = {
          ...user,
          email: '<EMAIL>',
          name: 'Updated Name',
          lastName: 'Updated LastName',
          companies: [...user.companies, testCompany.id],
        };

        await CoreUserRepository.update(updatedUser);

        const refreshedUser = await CoreUserRepository.findOneById(SEED_USER.id);
        expect(refreshedUser?.email).toBe('<EMAIL>');
        expect(refreshedUser?.name).toBe('Updated Name');
        expect(refreshedUser?.lastName).toBe('Updated LastName');
        expect(refreshedUser?.companies).toContain(testCompany.id);
      }
    });
  });

  describe('findOneByEmail', () => {
    it('should find user by email', async () => {
      const user = await CoreUserRepository.findOneByEmail(SEED_USER.email);

      expect(user).toBeDefined();
      expect(user?.email).toBe(SEED_USER.email);
      expect(user?.companies).toContain(SEED_COMPANY.id);
    });

    it('should return undefined for non-existent email', async () => {
      const user = await CoreUserRepository.findOneByEmail('<EMAIL>');
      expect(user).toBeUndefined();
    });
  });

  describe('findOneByPhoneNumber', () => {
    it('should find user by phone number', async () => {
      const user = await CoreUserRepository.findOneByPhoneNumber(SEED_USER.appPhoneNumber);

      expect(user).toBeDefined();
      expect(user?.phoneNumber).toBe(SEED_USER.appPhoneNumber);
      expect(user?.email).toBe(SEED_USER.email);
      expect(user?.companies).toContain(SEED_COMPANY.id);
    });

    it('should return undefined for non-existent phone number', async () => {
      const user = await CoreUserRepository.findOneByPhoneNumber('************');
      expect(user).toBeUndefined();
    });
  });
});
