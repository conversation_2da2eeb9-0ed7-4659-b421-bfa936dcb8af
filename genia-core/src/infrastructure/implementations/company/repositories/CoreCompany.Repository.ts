import CompanyRepository from '#application/company/repositories/Company.Repository';
import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import CompanyOperator from '#domain/aggregates/company/Company.Operator';
import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';
import CoreDatabase from '#infrastructure/databases/core/Core.Database';
import { dbCompany } from '#infrastructure/databases/core/types';

const { COMPANY } = TableNamesConfiguration;

const companySelect: Array<keyof dbCompany> = ['id', 'name', 'description', 'country', 'tributaryId'];

async function findOneByTributaryIdAndCountry(tributaryId: string, country: string): Promise<CompanyEntity | undefined> {
  const db = CoreDatabase.getClient();

  const company = await db(COMPANY).select(companySelect)
    .where('tributaryId', tributaryId)
    .andWhere('country', country)
    .first();

  if (!company) return undefined;

  return CompanyOperator.build(company);
}

async function findManyByIds(ids: string[]): Promise<CompanyEntity[]> {
  const db = CoreDatabase.getClient();

  const companies = await db(TableNamesConfiguration.COMPANY).select('*').whereIn('id', ids);

  return companies.map<CompanyEntity>((company) => CompanyOperator.build(company));
}

async function save(company: CompanyEntity): Promise<CompanyEntity> {
  const db = CoreDatabase.getClient();

  const companyRecord = (await db(COMPANY).insert({ ...company }).returning(companySelect).onConflict(['id'])
    .merge()
    .returning(companySelect))[0];

  return CompanyOperator.build(companyRecord);
}

async function findOneById(id: string): Promise<CompanyEntity | undefined> {
  const db = CoreDatabase.getClient();

  const company = await db(COMPANY).select(companySelect).where({ id }).first();

  if (!company) return undefined;

  return CompanyOperator.build(company);
}

const CoreCompanyRepository: CompanyRepository = {
  save,
  findOneByTributaryIdAndCountry,
  findManyByIds,
  findOneById,
};

export default CoreCompanyRepository;
