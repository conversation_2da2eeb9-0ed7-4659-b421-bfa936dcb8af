import { v4 } from 'uuid';

import { ApplicationError } from '#application/Common.Type';
import UpdateInventoryStockCommand from '#application/inventory/commands/UpdateInventoryStock.Command';
import InventoryErrorCodes from '#application/inventory/InventoryErrorCodes';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import InventoryOperator from '#domain/aggregates/inventory/Inventory.Operator';
import { InventoryHistoryEntity, InventoryMovementType } from '#domain/aggregates/inventory/InventoryHistory.Entity';
import InventoryHistoryOperator from '#domain/aggregates/inventory/InventoryHistory.Operator';

async function apply(command: UpdateInventoryStockCommand): Promise<InventoryHistoryEntity> {
  const {
    inventoryId, companyId, quantity, movementType, reason, userId,
  } = command;

  const inventory = await Registry.InventoryRepository.findOneById(inventoryId);

  if (!inventory) {
    throw new ApplicationError(
      'Inventory not found',
      Cause.NOT_FOUND,
      InventoryErrorCodes.INVENTORY_PRODUCT_NOT_FOUND,
    );
  }

  if (inventory.companyId !== companyId) {
    throw new ApplicationError(
      'Inventory does not belong to the company',
      Cause.FORBIDDEN,
      InventoryErrorCodes.INVENTORY_PRODUCT_FORBIDDEN,
    );
  }

  const newStock = movementType === InventoryMovementType.INBOUND ? inventory.stock + quantity : inventory.stock - quantity;

  const updatedInventory = InventoryOperator.update(inventory, { stock: newStock });
  const inventoryMovement = InventoryHistoryOperator.build({
    id: v4(),
    inventoryId,
    quantity,
    measurementUnit: inventory.measurementUnit,
    movementType,
    reason,
    userId,
    createdAt: new Date(),
  });

  return Registry.TransactionService.transactional(async () => {
    await Registry.InventoryRepository.save([updatedInventory]);

    const [history] = await Registry.InventoryHistoryRepository.save([inventoryMovement]);

    return history;
  });
}

export default {
  apply,
};
