import UserEntity from '#domain/aggregates/user/User.Entity';

export type CreateUser = Omit<UserEntity, 'id'>;

interface UserRepository {
  findOneByEmail(email: string): Promise<UserEntity | undefined>;
  findOneByPhoneNumber(phoneNumber: string): Promise<UserEntity | undefined>;
  createOne(user: CreateUser): Promise<UserEntity>;
  findOneById(id: string): Promise<UserEntity | undefined>;
  update(user: UserEntity): Promise<void>;
}

export default UserRepository;
