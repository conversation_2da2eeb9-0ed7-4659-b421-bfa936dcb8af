import { UserRole } from '#domain/aggregates/user/User.Entity';

import UserOperator, { UserOperatorBuildParams } from './User.Operator';

jest.unmock('./User.Operator');

describe('UserOperator', () => {
  describe('build', () => {
    it('should create a user with valid parameters and default role', () => {
      const userParams: UserOperatorBuildParams = {
        id: '123',
        email: '<EMAIL>',
        name: '<PERSON>',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company1'],
      };

      const user = UserOperator.build(userParams);

      expect(user).toEqual({
        id: '123',
        email: '<EMAIL>',
        name: '<PERSON>',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company1'],
        role: UserRole.USER, // Default role
      });
    });

    it('should create a user with specified role', () => {
      const userParams: UserOperatorBuildParams = {
        id: '456',
        email: '<EMAIL>',
        name: '<PERSON>',
        lastName: '<PERSON>',
        phoneNumber: '0987654321',
        companies: ['company1', 'company2'],
        role: UserRole.ADMIN,
      };

      const user = UserOperator.build(userParams);

      expect(user).toEqual({
        id: '456',
        email: '<EMAIL>',
        name: 'Jane',
        lastName: 'Smith',
        phoneNumber: '0987654321',
        companies: ['company1', 'company2'],
        role: UserRole.ADMIN,
      });
    });

    it('should throw error when creating user without companies', () => {
      const userParams: UserOperatorBuildParams = {
        id: '123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: [],
      };

      expect(() => {
        UserOperator.build(userParams);
      }).toThrow('User must have at least one company');
    });
  });

  describe('addCompany', () => {
    it('should add a new company to user', () => {
      const userParams: UserOperatorBuildParams = {
        id: '123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company1'],
      };

      const user = UserOperator.build(userParams);
      const updatedUser = UserOperator.addCompany(user, 'company2');

      expect(updatedUser.companies).toContain('company2');
      expect(updatedUser.companies.length).toBe(2);
      expect(updatedUser.companies).toEqual(['company1', 'company2']);
    });

    it('should throw error when adding duplicate company', () => {
      const userParams: UserOperatorBuildParams = {
        id: '123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company1'],
      };

      const user = UserOperator.build(userParams);

      expect(() => {
        UserOperator.addCompany(user, 'company1');
      }).toThrow('User already belongs to this company');
    });

    it('should return a new user object without mutating the original', () => {
      const userParams: UserOperatorBuildParams = {
        id: '123',
        email: '<EMAIL>',
        name: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        companies: ['company1'],
      };

      const user = UserOperator.build(userParams);
      const updatedUser = UserOperator.addCompany(user, 'company2');

      // Should return a different object reference
      expect(updatedUser).not.toBe(user);

      // Original user should have the same properties except companies array reference
      expect(user.id).toBe(updatedUser.id);
      expect(user.email).toBe(updatedUser.email);
      expect(user.name).toBe(updatedUser.name);
      expect(user.lastName).toBe(updatedUser.lastName);
      expect(user.phoneNumber).toBe(updatedUser.phoneNumber);
      expect(user.role).toBe(updatedUser.role);
    });
  });
});
