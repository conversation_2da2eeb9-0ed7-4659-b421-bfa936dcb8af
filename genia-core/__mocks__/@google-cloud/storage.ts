import * as storageModule from '@google-cloud/storage';
import { Storage as storage } from '@google-cloud/storage';

// Mock for Storage().bucket().file().getSignedUrl()
const getSignedUrlMock = jest.fn().mockResolvedValue(['https://mock-signed-url.com']);

const fileMock = jest.fn(() => ({
  getSignedUrl: getSignedUrlMock,
}));

const bucketMock = jest.fn(() => ({
  file: fileMock,
}));

const storageMock = jest.fn(() => ({
  bucket: bucketMock,
}));

const datastoreModuleMock: Partial<typeof storageModule> = {
  Storage: storageMock as unknown as typeof storage,
};

export const { Storage } = datastoreModuleMock;

export default datastoreModuleMock;
