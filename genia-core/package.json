{"name": "genia-core", "version": "1.7.2", "description": "Gen.ia core", "main": "./app.js", "engines": {"node": ">=20.0.0"}, "scripts": {"build:dev": "tsc -b -w", "build": "tsc --project tsconfig.build.json  && tsc-alias && cp package.json ./dist", "clean": "rm -f tsconfig.build.tsbuildinfo; rm -rf ./dist", "hasura:apply:dev": "hasura metadata apply --project hasura --envfile ../.env", "hasura:apply": "hasura metadata apply --project hasura", "hasura:export:dev": "hasura metadata export --project hasura --envfile ../.env", "hasura:export": "hasura metadata export --project hasura", "lint:fix": "yarn lint --fix", "lint": "eslint .", "migrate:dev": "knex --cwd ./src/infrastructure/databases/core migrate:latest", "migrate:make": "knex migrate:make --cwd ./src/infrastructure/databases/core", "migrate:unlock": "knex migrate:unlock --cwd ./src/infrastructure/databases/core", "migrate:rollback:dev": "knex --cwd ./src/infrastructure/databases/core migrate:rollback", "migrate:rollback": "knex migrate:rollback --cwd ./infrastructure/databases/core", "migrate": "knex migrate:latest --cwd ./infrastructure/databases/core", "prepare": "husky", "seed": "knex seed:run --cwd ./src/infrastructure/databases/core", "start:dev": "nodemon  ./src/infrastructure/App.ts", "start": "node dist/infrastructure/App.js", "test:coverage": "jest --coverage  && pnpm run test:integration", "test:integration": "jest --runInBand --coverage --force-exit --config ./jest.config.integration.ts", "test:watch": "jest --watch", "test": "jest"}, "author": "pitsDepot", "license": "UNLICENSED", "dependencies": {"@google-cloud/storage": "^7.16.0", "@types/pg": "^8.11.10", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "dotenv-expand": "^11.0.6", "express": "^4.19.2", "express-oauth2-jwt-bearer": "^1.6.0", "graphql": "^16.9.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "lodash": "^4.17.21", "pg": "^8.12.0", "pino": "^9.3.1", "pino-pretty": "^11.2.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsconfig-paths": "^4.2.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cookie-parser": "^1.4.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^8.5.1", "@types/lodash": "^4.17.7", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "concurrently": "^6.1.0", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.29.1", "hasura-cli": "2.36.1", "husky": "^9.1.5", "jest": "^29.7.0", "nodemon": "^3.1.0", "ts-jest": "^29.2.4", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "typescript": "^5.4.2"}}