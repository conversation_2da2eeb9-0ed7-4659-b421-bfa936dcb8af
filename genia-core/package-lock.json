{"name": "genia-core", "version": "1.7.2", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "genia-core", "version": "1.7.2", "license": "UNLICENSED", "dependencies": {"@google-cloud/storage": "^7.16.0", "@types/pg": "^8.11.10", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "dotenv-expand": "^11.0.6", "express": "^4.19.2", "express-oauth2-jwt-bearer": "^1.6.0", "graphql": "^16.9.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "lodash": "^4.17.21", "pg": "^8.12.0", "pino": "^9.3.1", "pino-pretty": "^11.2.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsconfig-paths": "^4.2.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cookie-parser": "^1.4.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^8.5.1", "@types/lodash": "^4.17.7", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "concurrently": "^6.1.0", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.29.1", "hasura-cli": "2.36.1", "husky": "^9.1.5", "jest": "^29.7.0", "nodemon": "^3.1.0", "ts-jest": "^29.2.4", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "typescript": "^5.4.2"}, "engines": {"node": ">=20.0.0"}}, "../node_modules/.pnpm/@google-cloud+storage@7.16.0/node_modules/@google-cloud/storage": {"version": "7.16.0", "license": "Apache-2.0", "dependencies": {"@google-cloud/paginator": "^5.0.0", "@google-cloud/projectify": "^4.0.0", "@google-cloud/promisify": "<4.1.0", "abort-controller": "^3.0.0", "async-retry": "^1.3.3", "duplexify": "^4.1.3", "fast-xml-parser": "^4.4.1", "gaxios": "^6.0.2", "google-auth-library": "^9.6.3", "html-entities": "^2.5.2", "mime": "^3.0.0", "p-limit": "^3.0.1", "retry-request": "^7.0.0", "teeny-request": "^9.0.0", "uuid": "^8.0.0"}, "devDependencies": {"@babel/cli": "^7.22.10", "@babel/core": "^7.22.11", "@google-cloud/pubsub": "^4.0.0", "@grpc/grpc-js": "^1.0.3", "@grpc/proto-loader": "^0.7.0", "@types/async-retry": "^1.4.3", "@types/duplexify": "^3.6.4", "@types/mime": "^3.0.0", "@types/mocha": "^9.1.1", "@types/mockery": "^1.4.29", "@types/node": "^22.0.0", "@types/node-fetch": "^2.1.3", "@types/proxyquire": "^1.3.28", "@types/request": "^2.48.4", "@types/sinon": "^17.0.0", "@types/tmp": "0.2.6", "@types/uuid": "^8.0.0", "@types/yargs": "^17.0.10", "c8": "^9.0.0", "form-data": "^4.0.0", "gapic-tools": "^0.4.0", "gts": "^5.0.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^3.0.0", "mocha": "^9.2.2", "mockery": "^2.1.0", "nise": "6.0.0", "nock": "~13.5.0", "node-fetch": "^2.6.7", "pack-n-play": "^2.0.0", "path-to-regexp": "6.3.0", "proxyquire": "^2.1.3", "sinon": "^18.0.0", "tmp": "^0.2.0", "typescript": "^5.1.6", "yargs": "^17.3.1"}, "engines": {"node": ">=14"}}, "../node_modules/.pnpm/@types+bcrypt@5.0.2/node_modules/@types/bcrypt": {"version": "5.0.2", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "../node_modules/.pnpm/@types+cookie-parser@1.4.7/node_modules/@types/cookie-parser": {"version": "1.4.7", "dev": true, "license": "MIT", "dependencies": {"@types/express": "*"}}, "../node_modules/.pnpm/@types+cors@2.8.17/node_modules/@types/cors": {"version": "2.8.17", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "../node_modules/.pnpm/@types+express@4.17.21/node_modules/@types/express": {"version": "4.17.21", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "../node_modules/.pnpm/@types+jest@29.5.12/node_modules/@types/jest": {"version": "29.5.12", "dev": true, "license": "MIT", "dependencies": {"expect": "^29.0.0", "pretty-format": "^29.0.0"}}, "../node_modules/.pnpm/@types+jsonwebtoken@8.5.9/node_modules/@types/jsonwebtoken": {"version": "8.5.9", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "../node_modules/.pnpm/@types+lodash@4.17.7/node_modules/@types/lodash": {"version": "4.17.7", "dev": true, "license": "MIT"}, "../node_modules/.pnpm/@types+pg@8.11.10/node_modules/@types/pg": {"version": "8.11.10", "license": "MIT", "dependencies": {"@types/node": "*", "pg-protocol": "*", "pg-types": "^4.0.1"}}, "../node_modules/.pnpm/@types+swagger-jsdoc@6.0.4/node_modules/@types/swagger-jsdoc": {"version": "6.0.4", "dev": true, "license": "MIT"}, "../node_modules/.pnpm/@types+swagger-ui-express@4.1.6/node_modules/@types/swagger-ui-express": {"version": "4.1.6", "dev": true, "license": "MIT", "dependencies": {"@types/express": "*", "@types/serve-static": "*"}}, "../node_modules/.pnpm/@types+uuid@9.0.8/node_modules/@types/uuid": {"version": "9.0.8", "dev": true, "license": "MIT"}, "../node_modules/.pnpm/@typescript-eslint+eslint-plugin@7.18.0_@typescript-eslint+parser@7.18.0_eslint@8.57.0_typesc_d25aaf2yjh6wdbb4ovryjlos5q/node_modules/@typescript-eslint/eslint-plugin": {"version": "7.18.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "7.18.0", "@typescript-eslint/type-utils": "7.18.0", "@typescript-eslint/utils": "7.18.0", "@typescript-eslint/visitor-keys": "7.18.0", "graphemer": "^1.4.0", "ignore": "^5.3.1", "natural-compare": "^1.4.0", "ts-api-utils": "^1.3.0"}, "devDependencies": {"@jest/types": "29.6.3", "@types/marked": "^5.0.2", "@types/mdast": "^4.0.3", "@types/natural-compare": "*", "@typescript-eslint/rule-schema-to-typescript-types": "7.18.0", "@typescript-eslint/rule-tester": "7.18.0", "ajv": "^6.12.6", "cross-env": "^7.0.3", "cross-fetch": "*", "eslint": "*", "espree": "^10.0.1", "jest": "29.7.0", "jest-specific-snapshot": "^8.0.0", "json-schema": "*", "markdown-table": "^3.0.3", "marked": "^5.1.2", "mdast-util-from-markdown": "^2.0.0", "mdast-util-mdx": "^3.0.0", "micromark-extension-mdxjs": "^3.0.0", "prettier": "^3.2.5", "rimraf": "*", "title-case": "^3.0.3", "tsx": "*", "typescript": "*", "unist-util-visit": "^5.0.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^7.0.0", "eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "../node_modules/.pnpm/@typescript-eslint+parser@7.18.0_eslint@8.57.0_typescript@5.5.4/node_modules/@typescript-eslint/parser": {"version": "7.18.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "7.18.0", "@typescript-eslint/types": "7.18.0", "@typescript-eslint/typescript-estree": "7.18.0", "@typescript-eslint/visitor-keys": "7.18.0", "debug": "^4.3.4"}, "devDependencies": {"@jest/types": "29.6.3", "@types/glob": "*", "downlevel-dts": "*", "glob": "*", "jest": "29.7.0", "prettier": "^3.2.5", "rimraf": "*", "typescript": "*"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "../node_modules/.pnpm/ajv-formats@3.0.1_ajv@8.17.1/node_modules/ajv-formats": {"version": "3.0.1", "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^8.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.3.2", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "../node_modules/.pnpm/ajv@8.17.1/node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "devDependencies": {"@ajv-validator/config": "^0.5.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@types/chai": "^4.3.11", "@types/mocha": "^10.0.6", "@types/node": "^20.11.30", "@types/require-from-string": "^1.2.3", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "ajv-formats": "^3.0.1", "browserify": "^17.0.0", "chai": "^4.4.1", "cross-env": "^7.0.3", "dayjs": "^1.11.10", "dayjs-plugin-utc": "^0.1.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "glob": "^10.3.10", "husky": "^9.0.11", "if-node-version": "^1.1.1", "jimp": "^0.22.10", "js-beautify": "^1.15.1", "json-schema-test": "^2.0.0", "karma": "^6.4.2", "karma-chrome-launcher": "^3.2.0", "karma-mocha": "^2.0.1", "lint-staged": "^15.2.2", "mocha": "^10.3.0", "module-from-string": "^3.3.0", "node-fetch": "^3.3.2", "nyc": "^15.1.0", "prettier": "3.0.3", "re2": "^1.20.9", "rollup": "^2.79.1", "rollup-plugin-terser": "^7.0.2", "ts-node": "^10.9.2", "tsify": "^5.0.4", "typescript": "5.3.3", "uri-js": "^4.4.1"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "../node_modules/.pnpm/bcrypt@5.1.1/node_modules/bcrypt": {"version": "5.1.1", "hasInstallScript": true, "license": "MIT", "dependencies": {"@mapbox/node-pre-gyp": "^1.0.11", "node-addon-api": "^5.0.0"}, "devDependencies": {"jest": "^29.6.2"}, "engines": {"node": ">= 10.0.0"}}, "../node_modules/.pnpm/concurrently@6.5.1/node_modules/concurrently": {"version": "6.5.1", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "date-fns": "^2.16.1", "lodash": "^4.17.21", "rxjs": "^6.6.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0", "tree-kill": "^1.2.2", "yargs": "^16.2.0"}, "bin": {"concurrently": "bin/concurrently.js"}, "devDependencies": {"coveralls": "^3.1.0", "eslint": "^7.17.0", "jest": "^26.6.3", "jest-create-mock-instance": "^1.1.0"}, "engines": {"node": ">=10.0.0"}}, "../node_modules/.pnpm/cookie-parser@1.4.6/node_modules/cookie-parser": {"version": "1.4.6", "license": "MIT", "dependencies": {"cookie": "0.4.1", "cookie-signature": "1.0.6"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.2", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "9.1.3", "nyc": "15.1.0", "supertest": "6.1.6"}, "engines": {"node": ">= 0.8.0"}}, "../node_modules/.pnpm/cors@2.8.5/node_modules/cors": {"version": "2.8.5", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "devDependencies": {"after": "0.8.2", "eslint": "2.13.1", "express": "4.16.3", "mocha": "5.2.0", "nyc": "13.1.0", "supertest": "3.3.0"}, "engines": {"node": ">= 0.10"}}, "../node_modules/.pnpm/dotenv-expand@11.0.6/node_modules/dotenv-expand": {"version": "11.0.6", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dotenv": "^16.4.4"}, "devDependencies": {"@types/node": "^18.11.3", "standard": "^16.0.4", "standard-version": "^9.5.0", "tap": "^16.3.0", "typescript": "^4.5.4"}, "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "../node_modules/.pnpm/dotenv@16.4.5/node_modules/dotenv": {"version": "16.4.5", "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"@definitelytyped/dtslint": "^0.0.133", "@types/node": "^18.11.3", "decache": "^4.6.1", "sinon": "^14.0.1", "standard": "^17.0.0", "standard-markdown": "^7.1.0", "standard-version": "^9.5.0", "tap": "^16.3.0", "tar": "^6.1.11", "typescript": "^4.8.4"}, "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "../node_modules/.pnpm/eslint-config-airbnb-base@15.0.0_eslint-plugin-import@2.29.1_eslint@8.57.0/node_modules/eslint-config-airbnb-base": {"version": "15.0.0", "dev": true, "license": "MIT", "dependencies": {"confusing-browser-globals": "^1.0.10", "object.assign": "^4.1.2", "object.entries": "^1.1.5", "semver": "^6.3.0"}, "devDependencies": {"@babel/runtime": "^7.16.0", "babel-preset-airbnb": "^4.5.0", "babel-tape-runner": "^3.0.0", "eclint": "^2.8.1", "eslint": "^7.32.0 || ^8.2.0", "eslint-find-rules": "^4.0.0", "eslint-plugin-import": "^2.25.2", "in-publish": "^2.0.1", "safe-publish-latest": "^2.0.0", "tape": "^5.3.1"}, "engines": {"node": "^10.12.0 || >=12.0.0"}, "peerDependencies": {"eslint": "^7.32.0 || ^8.2.0", "eslint-plugin-import": "^2.25.2"}}, "../node_modules/.pnpm/eslint-import-resolver-typescript@3.6.3_@typescript-eslint+parser@7.18.0_eslint@8.57.0_typesc_ueqthu3mle6dp6rd37pv2qkc2q/node_modules/eslint-import-resolver-typescript": {"version": "3.6.3", "dev": true, "license": "ISC", "dependencies": {"@nolyfill/is-core-module": "1.0.39", "debug": "^4.3.5", "enhanced-resolve": "^5.15.0", "eslint-module-utils": "^2.8.1", "fast-glob": "^3.3.2", "get-tsconfig": "^4.7.5", "is-bun-module": "^1.0.2", "is-glob": "^4.0.3"}, "devDependencies": {"@1stg/lib-config": "^12.0.1", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.7", "@commitlint/cli": "^17.8.1", "@mozilla/glean": "^3.0.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/is-glob": "^4.0.4", "@types/node": "^18.19.42", "@types/unist": "^2.0.10", "dummy.js": "link:dummy.js", "eslint": "^8.57.0", "eslint-import-resolver-typescript": "link:.", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "lint-staged": "^13.3.0", "npm-run-all2": "^5.0.2", "prettier": "^2.8.8", "react": "^18.2.0", "simple-git-hooks": "^2.9.0", "size-limit": "^11.0.0", "size-limit-preset-node-lib": "^0.3.0", "type-coverage": "^2.27.0", "typescript": "^5.3.2"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}}, "../node_modules/.pnpm/eslint-plugin-import@2.29.1_@typescript-eslint+parser@7.18.0_eslint@8.57.0_typescript@5.5.4___blq45kzmd5c2feotuyvehg4ffy/node_modules/eslint-plugin-import": {"version": "2.29.1", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.7", "array.prototype.findlastindex": "^1.2.3", "array.prototype.flat": "^1.3.2", "array.prototype.flatmap": "^1.3.2", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.8.0", "hasown": "^2.0.0", "is-core-module": "^2.13.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.7", "object.groupby": "^1.0.1", "object.values": "^1.1.7", "semver": "^6.3.1", "tsconfig-paths": "^3.15.0"}, "devDependencies": {"@angular-eslint/template-parser": "^13.5.0", "@eslint/import-test-order-redirect-scoped": "file:./tests/files/order-redirect-scoped", "@test-scope/some-module": "file:./tests/files/symlinked-module", "@typescript-eslint/parser": "^2.23.0 || ^3.3.0 || ^4.29.3 || ^5.10.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-eslint": "=8.0.3 || ^8.2.6", "babel-plugin-istanbul": "^4.1.6", "babel-plugin-module-resolver": "^2.7.1", "babel-preset-airbnb": "^2.6.0", "babel-preset-flow": "^6.23.0", "babel-register": "^6.26.0", "babylon": "^6.18.0", "chai": "^4.3.10", "cross-env": "^4.0.0", "escope": "^3.6.0", "eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "eslint-doc-generator": "^1.6.1", "eslint-import-resolver-node": "file:./resolvers/node", "eslint-import-resolver-typescript": "^1.0.2 || ^1.1.1", "eslint-import-resolver-webpack": "file:./resolvers/webpack", "eslint-import-test-order-redirect": "file:./tests/files/order-redirect", "eslint-module-utils": "file:./utils", "eslint-plugin-eslint-plugin": "^2.3.0", "eslint-plugin-import": "2.x", "eslint-plugin-json": "^2.1.2", "fs-copy-file-sync": "^1.1.1", "glob": "^7.2.3", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "linklocal": "^2.8.2", "lodash.isarray": "^4.0.0", "markdownlint-cli": "^0.38.0", "mocha": "^3.5.3", "npm-which": "^3.0.1", "nyc": "^11.9.0", "redux": "^3.7.2", "rimraf": "^2.7.1", "safe-publish-latest": "^2.0.0", "sinon": "^2.4.1", "typescript": "^2.8.1 || ~3.9.5 || ~4.5.2", "typescript-eslint-parser": "^15 || ^20 || ^22"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8"}}, "../node_modules/.pnpm/eslint@8.57.0/node_modules/eslint": {"version": "8.57.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.0", "@humanwhocodes/config-array": "^0.11.14", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "devDependencies": {"@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "@wdio/browser-runner": "^8.14.6", "@wdio/cli": "^8.14.6", "@wdio/concise-reporter": "^8.14.0", "@wdio/globals": "^8.14.6", "@wdio/mocha-framework": "^8.14.0", "babel-loader": "^8.0.5", "c8": "^7.12.0", "chai": "^4.0.1", "cheerio": "^0.22.0", "common-tags": "^1.8.0", "core-js": "^3.1.3", "ejs": "^3.0.2", "eslint": "file:.", "eslint-config-eslint": "file:packages/eslint-config-eslint", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-eslint-plugin": "^5.2.1", "eslint-plugin-internal-rules": "file:tools/internal-rules", "eslint-plugin-jsdoc": "^46.2.5", "eslint-plugin-n": "^16.6.0", "eslint-plugin-unicorn": "^49.0.0", "eslint-release": "^3.2.0", "eslump": "^3.0.0", "esprima": "^4.0.1", "fast-glob": "^3.2.11", "fs-teardown": "^0.1.3", "glob": "^7.1.6", "got": "^11.8.3", "gray-matter": "^4.0.3", "lint-staged": "^11.0.0", "load-perf": "^0.2.0", "markdown-it": "^12.2.0", "markdown-it-container": "^3.0.0", "markdownlint": "^0.32.0", "markdownlint-cli": "^0.37.0", "marked": "^4.0.8", "memfs": "^3.0.1", "metascraper": "^5.25.7", "metascraper-description": "^5.25.7", "metascraper-image": "^5.29.3", "metascraper-logo": "^5.25.7", "metascraper-logo-favicon": "^5.25.7", "metascraper-title": "^5.25.7", "mocha": "^8.3.2", "mocha-junit-reporter": "^2.0.0", "node-polyfill-webpack-plugin": "^1.0.3", "npm-license": "^0.3.3", "pirates": "^4.0.5", "progress": "^2.0.3", "proxyquire": "^2.0.1", "recast": "^0.23.0", "regenerator-runtime": "^0.14.0", "rollup-plugin-node-polyfills": "^0.2.1", "semver": "^7.5.3", "shelljs": "^0.8.2", "sinon": "^11.0.0", "vite-plugin-commonjs": "^0.10.0", "webdriverio": "^8.14.6", "webpack": "^5.23.0", "webpack-cli": "^4.5.0", "yorkie": "^2.0.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "../node_modules/.pnpm/express-oauth2-jwt-bearer@1.6.0/node_modules/express-oauth2-jwt-bearer": {"version": "1.6.0", "license": "MIT", "dependencies": {"jose": "^4.13.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^13.3.0", "@tsconfig/node12": "^1.0.11", "@types/express": "^4.17.17", "@types/jest": "^27.5.2", "@types/node": "^14.18.42", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "eslint": "^8.37.0", "express": "^4.18.2", "got": "^11.8.6", "jest": "^29.5.0", "jest-junit": "^13.2.0", "nock": "^13.3.0", "prettier": "~2.5.1", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-typescript2": "^0.31.2", "ts-jest": "^29.0.5", "tslib": "^2.5.0", "typescript": "^5.0.2"}, "engines": {"node": "^12.19.0 || ^14.15.0 || ^16.13.0 || ^18.12.0 || ^20.2.0"}}, "../node_modules/.pnpm/express@4.19.2/node_modules/express": {"version": "4.19.2", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.2", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.6.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.2.0", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.7", "qs": "6.11.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.18.0", "serve-static": "1.15.0", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "devDependencies": {"after": "0.8.2", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "ejs": "3.1.9", "eslint": "8.47.0", "express-session": "1.17.2", "hbs": "4.2.0", "marked": "0.7.0", "method-override": "3.0.0", "mocha": "10.2.0", "morgan": "1.10.0", "nyc": "15.1.0", "pbkdf2-password": "1.2.1", "supertest": "6.3.0", "vhost": "~3.0.2"}, "engines": {"node": ">= 0.10.0"}}, "../node_modules/.pnpm/graphql@16.9.0/node_modules/graphql": {"version": "16.9.0", "license": "MIT", "engines": {"node": "^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0"}}, "../node_modules/.pnpm/hasura-cli@2.36.1/node_modules/hasura-cli": {"version": "2.36.1", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"axios": "^0.21.1", "chalk": "^2.4.2"}, "bin": {"hasura": "<PERSON><PERSON>"}, "devDependencies": {"@commitlint/cli": "^12.0.1", "@commitlint/config-conventional": "^12.0.1", "@swc/core": "^1.3.75", "@types/jest": "^24.0.18", "@types/semver": "^7.5.0", "@typescript-eslint/eslint-plugin": "^2.2.0", "@typescript-eslint/parser": "^2.2.0", "commitizen": "^4.0.3", "concurrently": "^6.0.0", "cross-env": "^6.0.0", "cz-conventional-changelog": "^3.0.2", "dirname-filename-esm": "^1.1.1", "dotenv-cli": "^2.0.1", "eslint": "5.3.0", "eslint-config-airbnb-base": "13.2.0", "eslint-config-prettier": "^6.0.0", "eslint-import-resolver-typescript": "^1.1.1", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^22.9.0", "eslint-plugin-json": "^1.4.0", "eslint-plugin-markdown": "^1.0.0", "eslint-plugin-prettier": "^3.1.0", "execa": "^7.2.0", "husky": "^7.0.0", "jest": "^26.6.3", "jest-junit": "^7.0.0", "lint-staged": "^11.1.1", "markdownlint-cli": "^0.22.0", "nodemon": "^1.19.2", "octokit": "^3.1.0", "prettier": "^1.18.2", "semver": "^7.5.4", "shx": "^0.3.2", "ts-jest": "^26.5.4", "ts-node": "^10.9.1", "ts-node-dev": "^1.1.6", "typescript": "^3.6.3", "verdaccio": "^4.3.0"}, "engines": {"node": ">=8"}}, "../node_modules/.pnpm/husky@9.1.5/node_modules/husky": {"version": "9.1.5", "dev": true, "license": "MIT", "bin": {"husky": "bin.js"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/typicode"}}, "../node_modules/.pnpm/jest@29.7.0_@types+node@22.10.2_babel-plugin-macros@3.1.0_ts-node@10.9.2_@types+node@22.10.2_typescript@5.5.4_/node_modules/jest": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/core": "^29.7.0", "@jest/types": "^29.6.3", "import-local": "^3.0.2", "jest-cli": "^29.7.0"}, "bin": {"jest": "bin/jest.js"}, "devDependencies": {"@tsd/typescript": "^5.0.4", "tsd-lite": "^0.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "../node_modules/.pnpm/jsonwebtoken@9.0.2/node_modules/jsonwebtoken": {"version": "9.0.2", "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "devDependencies": {"atob": "^2.1.2", "chai": "^4.1.2", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "eslint": "^4.19.1", "mocha": "^5.2.0", "nsp": "^2.6.2", "nyc": "^11.9.0", "sinon": "^6.0.0"}, "engines": {"node": ">=12", "npm": ">=6"}}, "../node_modules/.pnpm/knex@3.1.0_pg@8.12.0/node_modules/knex": {"version": "3.1.0", "license": "MIT", "dependencies": {"colorette": "2.0.19", "commander": "^10.0.0", "debug": "4.3.4", "escalade": "^3.1.1", "esm": "^3.2.25", "get-package-type": "^0.1.0", "getopts": "2.3.0", "interpret": "^2.2.0", "lodash": "^4.17.21", "pg-connection-string": "2.6.2", "rechoir": "^0.8.0", "resolve-from": "^5.0.0", "tarn": "^3.0.2", "tildify": "2.0.0"}, "bin": {"knex": "bin/cli.js"}, "devDependencies": {"@tsconfig/recommended": "^1.0.1", "@types/node": "^20.4.0", "better-sqlite3": "^9.1.1", "chai": "^4.3.6", "chai-as-promised": "^7.1.1", "chai-subset-in-order": "^3.1.0", "cli-testlab": "^2.2.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "dtslint": "4.2.1", "eslint": "^8.54.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-mocha-no-only": "^1.1.1", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jake": "^10.8.5", "JSONStream": "^1.3.5", "lint-staged": "^13.0.0", "mocha": "^10.0.0", "mock-fs": "^5.1.4", "mysql": "^2.18.1", "mysql2": "^3.2.0", "nyc": "^15.1.0", "oracledb": "^6.1.0", "pg": "^8.8.0", "pg-query-stream": "^4.2.4", "prettier": "2.8.7", "rimraf": "^5.0.5", "sinon": "^15.0.1", "sinon-chai": "^3.7.0", "source-map-support": "^0.5.21", "sqlite3": "^5.0.11", "tap-spec": "^5.0.0", "tape": "^5.6.0", "tedious": "^16.6.1", "toxiproxy-node-client": "^2.0.6", "ts-node": "^10.9.1", "tsd": "^0.28.1", "typescript": "5.0.4"}, "engines": {"node": ">=16"}, "peerDependenciesMeta": {"better-sqlite3": {"optional": true}, "mysql": {"optional": true}, "mysql2": {"optional": true}, "pg": {"optional": true}, "pg-native": {"optional": true}, "sqlite3": {"optional": true}, "tedious": {"optional": true}}}, "../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "../node_modules/.pnpm/nodemon@3.1.4/node_modules/nodemon": {"version": "3.1.4", "dev": true, "license": "MIT", "dependencies": {"chokidar": "^3.5.2", "debug": "^4", "ignore-by-default": "^1.0.1", "minimatch": "^3.1.2", "pstree.remy": "^1.1.8", "semver": "^7.5.3", "simple-update-notifier": "^2.0.0", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.5"}, "bin": {"nodemon": "bin/nodemon.js"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "async": "1.4.2", "coffee-script": "~1.7.1", "eslint": "^7.32.0", "husky": "^7.0.4", "mocha": "^2.5.3", "nyc": "^15.1.0", "proxyquire": "^1.8.0", "semantic-release": "^18.0.0", "should": "~4.0.0"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nodemon"}}, "../node_modules/.pnpm/pg@8.12.0/node_modules/pg": {"version": "8.12.0", "license": "MIT", "dependencies": {"pg-connection-string": "^2.6.4", "pg-pool": "^3.6.2", "pg-protocol": "^1.6.1", "pg-types": "^2.1.0", "pgpass": "1.x"}, "devDependencies": {"@cloudflare/workers-types": "^4.20230404.0", "async": "2.6.4", "bluebird": "3.5.2", "co": "4.6.0", "pg-copy-streams": "0.3.0", "typescript": "^4.0.3", "workerd": "^1.20230419.0", "wrangler": "3.58.0"}, "engines": {"node": ">= 8.0.0"}, "optionalDependencies": {"pg-cloudflare": "^1.1.1"}, "peerDependencies": {"pg-native": ">=3.0.1"}, "peerDependenciesMeta": {"pg-native": {"optional": true}}}, "../node_modules/.pnpm/pino-pretty@11.2.2/node_modules/pino-pretty": {"version": "11.2.2", "license": "MIT", "dependencies": {"colorette": "^2.0.7", "dateformat": "^4.6.3", "fast-copy": "^3.0.2", "fast-safe-stringify": "^2.1.1", "help-me": "^5.0.0", "joycon": "^3.1.1", "minimist": "^1.2.6", "on-exit-leak-free": "^2.1.0", "pino-abstract-transport": "^1.0.0", "pump": "^3.0.0", "readable-stream": "^4.0.0", "secure-json-parse": "^2.4.0", "sonic-boom": "^4.0.1", "strip-json-comments": "^3.1.1"}, "bin": {"pino-pretty": "bin.js"}, "devDependencies": {"@arethetypeswrong/cli": "^0.15.3", "@types/node": "^20.1.0", "fastbench": "^1.0.1", "pino": "^9.0.0", "pre-commit": "^1.2.2", "rimraf": "^3.0.2", "semver": "^7.6.0", "snazzy": "^9.0.0", "standard": "^17.0.0", "tap": "^16.0.0", "tsd": "^0.31.0", "typescript": "^5.0.2"}}, "../node_modules/.pnpm/pino@9.3.2/node_modules/pino": {"version": "9.3.2", "license": "MIT", "dependencies": {"atomic-sleep": "^1.0.0", "fast-redact": "^3.1.1", "on-exit-leak-free": "^2.1.0", "pino-abstract-transport": "^1.2.0", "pino-std-serializers": "^7.0.0", "process-warning": "^4.0.0", "quick-format-unescaped": "^4.0.3", "real-require": "^0.2.0", "safe-stable-stringify": "^2.3.1", "sonic-boom": "^4.0.1", "thread-stream": "^3.0.0"}, "bin": {"pino": "bin.js"}, "devDependencies": {"@types/flush-write-stream": "^1.0.0", "@types/node": "^20.2.3", "@types/tap": "^15.0.6", "@yao-pkg/pkg": "5.12.0", "airtap": "4.0.4", "benchmark": "^2.1.4", "bole": "^5.0.5", "bunyan": "^1.8.14", "debug": "^4.3.4", "docsify-cli": "^4.4.4", "eslint": "^8.17.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "15.7.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.0.0", "execa": "^5.0.0", "fastbench": "^1.0.1", "flush-write-stream": "^2.0.0", "import-fresh": "^3.2.1", "jest": "^29.0.3", "log": "^6.0.0", "loglevel": "^1.6.7", "midnight-smoker": "1.1.1", "pino-pretty": "^11.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "pump": "^3.0.0", "rimraf": "^6.0.1", "semver": "^7.3.7", "split2": "^4.0.0", "steed": "^1.1.3", "strip-ansi": "^6.0.0", "tap": "^16.2.0", "tape": "^5.5.3", "through2": "^4.0.0", "ts-node": "^10.9.1", "tsd": "^0.31.0", "typescript": "^5.1.3", "winston": "^3.7.2"}}, "../node_modules/.pnpm/swagger-jsdoc@6.2.8_openapi-types@12.1.3/node_modules/swagger-jsdoc": {"version": "6.2.8", "license": "MIT", "dependencies": {"commander": "6.2.0", "doctrine": "3.0.0", "glob": "7.1.6", "lodash.mergewith": "^4.6.2", "swagger-parser": "^10.0.3", "yaml": "2.0.0-1"}, "bin": {"swagger-jsdoc": "bin/swagger-jsdoc.js"}, "devDependencies": {"body-parser": "1.19.0", "eslint": "8.9.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-prettier": "6.15.0", "eslint-loader": "4.0.2", "eslint-plugin-import": "2.25.4", "eslint-plugin-jest": "26.1.1", "eslint-plugin-prettier": "3.1.4", "express": "4.17.1", "husky": "7.0.4", "jest": "^26.6.1", "lint-staged": "10.5.2", "npm-run-all": "4.1.5", "prettier": "2.2.0", "supertest": "6.0.1"}, "engines": {"node": ">=12.0.0"}}, "../node_modules/.pnpm/swagger-ui-express@5.0.1_express@4.19.2/node_modules/swagger-ui-express": {"version": "5.0.1", "license": "MIT", "dependencies": {"swagger-ui-dist": ">=5.0.0"}, "devDependencies": {"es6-shim": "0.35.8", "express": "^4.19.2", "istanbul-badges-readme": "^1.9.0", "mocha": "^10.4.0", "nyc": "^15.1.0", "puppeteer": "22.8.2"}, "engines": {"node": ">= v0.10.32"}, "peerDependencies": {"express": ">=4.0.0 || >=5.0.0-beta"}}, "../node_modules/.pnpm/ts-jest@29.2.5_@babel+core@7.25.2_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@29.7.0_ewwkr2bge6y26cvjwby3vgtj7i/node_modules/ts-jest": {"version": "29.2.5", "dev": true, "license": "MIT", "dependencies": {"bs-logger": "^0.2.6", "ejs": "^3.1.10", "fast-json-stable-stringify": "^2.1.0", "jest-util": "^29.0.0", "json5": "^2.2.3", "lodash.memoize": "^4.1.2", "make-error": "^1.3.6", "semver": "^7.6.3", "yargs-parser": "^21.1.1"}, "bin": {"ts-jest": "cli.js"}, "devDependencies": {"@commitlint/cli": "~18.6.1", "@commitlint/config-angular": "~18.6.1", "@jest/globals": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/babel__core": "^7.20.5", "@types/ejs": "^3.1.5", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.12", "@types/js-yaml": "^4.0.9", "@types/lodash.camelcase": "^4.3.9", "@types/lodash.memoize": "^4.1.9", "@types/lodash.set": "^4.3.9", "@types/micromatch": "^4.0.9", "@types/node": "20.16.1", "@types/semver": "^7.5.8", "@types/yargs": "^17.0.33", "@types/yargs-parser": "21.0.3", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "babel-jest": "^29.7.0", "conventional-changelog-cli": "^5.0.0", "esbuild": "~0.21.5", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^28.8.0", "eslint-plugin-jsdoc": "^48.11.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^4.2.1", "execa": "5.1.1", "fs-extra": "^11.2.0", "glob": "^10.2.6", "glob-gitignore": "^1.0.14", "husky": "~4.3.8", "jest": "^29.7.0", "js-yaml": "^4.1.0", "json-schema-to-typescript": "^13.1.2", "lint-staged": "^15.2.9", "prettier": "^2.8.8", "typescript": "~5.5.4"}, "engines": {"node": "^14.15.0 || ^16.10.0 || ^18.0.0 || >=20.0.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.0 <8", "@jest/transform": "^29.0.0", "@jest/types": "^29.0.0", "babel-jest": "^29.0.0", "jest": "^29.0.0", "typescript": ">=4.3 <6"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "@jest/transform": {"optional": true}, "@jest/types": {"optional": true}, "babel-jest": {"optional": true}, "esbuild": {"optional": true}}}, "../node_modules/.pnpm/ts-node@10.9.2_@types+node@22.10.2_typescript@5.5.4/node_modules/ts-node": {"version": "10.9.2", "dev": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "devDependencies": {"@microsoft/api-extractor": "^7.19.4", "@swc/core": "^1.3.100", "@swc/wasm": "^1.3.100", "@types/diff": "^4.0.2", "@types/lodash": "^4.14.151", "@types/node": "13.13.5", "@types/proper-lockfile": "^4.1.2", "@types/proxyquire": "^1.3.28", "@types/react": "^16.14.19", "@types/rimraf": "^3.0.0", "@types/semver": "^7.1.0", "@yarnpkg/fslib": "^2.4.0", "ava": "^3.15.0", "axios": "^0.21.1", "dprint": "^0.25.0", "expect": "^27.0.2", "get-stream": "^6.0.0", "lodash": "^4.17.15", "ntypescript": "^1.201507091536.1", "nyc": "^15.0.1", "outdent": "^0.8.0", "proper-lockfile": "^4.1.2", "proxyquire": "^2.0.0", "react": "^16.14.0", "rimraf": "^3.0.0", "semver": "^7.1.3", "throat": "^6.0.1", "typedoc": "^0.22.10", "typescript": "4.7.4", "typescript-json-schema": "^0.53.0", "util.promisify": "^1.0.1"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "../node_modules/.pnpm/tsc-alias@1.8.10/node_modules/tsc-alias": {"version": "1.8.10", "dev": true, "license": "MIT", "dependencies": {"chokidar": "^3.5.3", "commander": "^9.0.0", "globby": "^11.0.4", "mylas": "^2.1.9", "normalize-path": "^3.0.0", "plimit-lit": "^1.2.6"}, "bin": {"tsc-alias": "dist/bin/index.js"}, "devDependencies": {"@types/jest": "^27.4.0", "@types/node": "^17.0.18", "@types/rimraf": "^3.0.2", "@types/shelljs": "^0.8.11", "husky": "^7.0.4", "jest": "^27.5.1", "prettier": "^2.5.1", "rimraf": "^3.0.2", "shelljs": "^0.8.5", "ts-jest": "^27.1.3", "typescript": "^4.5.5"}}, "../node_modules/.pnpm/tsconfig-paths@4.2.0/node_modules/tsconfig-paths": {"version": "4.2.0", "license": "MIT", "dependencies": {"json5": "^2.2.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/minimist": "^1.2.2", "@types/node": "^6.0.54", "@types/strip-bom": "^3.0.0", "@types/strip-json-comments": "^0.0.30", "@typescript-eslint/eslint-plugin": "^5.22.0", "@typescript-eslint/parser": "^5.22.0", "eslint": "^8.14.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^39.2.9", "husky": "^4.2.5", "jest": "^27.3.1", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "rimraf": "^2.6.2", "ts-jest": "^27.0.7", "ts-node": "^10.7.0", "typescript": "^4.5.2"}, "engines": {"node": ">=6"}}, "../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript": {"version": "5.5.4", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "devDependencies": {"@dprint/formatter": "^0.3.0", "@dprint/typescript": "0.91.0", "@esfx/canceltoken": "^1.0.0", "@octokit/rest": "^20.1.1", "@types/chai": "^4.3.16", "@types/microsoft__typescript-etw": "^0.1.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.6", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.3", "@typescript-eslint/eslint-plugin": "^7.11.0", "@typescript-eslint/parser": "^7.11.0", "@typescript-eslint/utils": "^7.11.0", "azure-devops-node-api": "^13.0.0", "c8": "^9.1.0", "chai": "^4.4.1", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.46.1", "esbuild": "^0.21.4", "eslint": "^8.57.0", "eslint-formatter-autolinkable-stylish": "^1.3.0", "eslint-plugin-local": "^4.2.2", "fast-xml-parser": "^4.4.0", "glob": "^10.4.1", "hereby": "^1.8.9", "jsonc-parser": "^3.2.1", "minimist": "^1.2.8", "mocha": "^10.4.0", "mocha-fivemat-progress-reporter": "^0.1.0", "ms": "^2.1.3", "node-fetch": "^3.3.2", "playwright": "^1.44.1", "source-map-support": "^0.5.21", "tslib": "^2.6.2", "typescript": "^5.4.5", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}}, "../node_modules/.pnpm/uuid@10.0.0/node_modules/uuid": {"version": "10.0.0", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "devDependencies": {"@babel/cli": "7.24.6", "@babel/core": "7.24.6", "@babel/eslint-parser": "7.24.6", "@babel/plugin-syntax-import-attributes": "7.24.6", "@babel/preset-env": "7.24.6", "@commitlint/cli": "19.3.0", "@commitlint/config-conventional": "19.2.2", "@wdio/browserstack-service": "7.16.10", "@wdio/cli": "7.16.10", "@wdio/jasmine-framework": "7.16.6", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@wdio/static-server-service": "7.16.6", "bundlewatch": "0.3.3", "eslint": "9.4.0", "eslint-plugin-prettier": "5.1.3", "globals": "15.3.0", "husky": "9.0.11", "jest": "29.7.0", "lint-staged": "15.2.5", "neostandard": "0.5.1", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "3.3.0", "random-seed": "0.3.0", "runmd": "1.3.9", "standard-version": "9.5.0"}}, "node_modules/@google-cloud/storage": {"resolved": "../node_modules/.pnpm/@google-cloud+storage@7.16.0/node_modules/@google-cloud/storage", "link": true}, "node_modules/@types/bcrypt": {"resolved": "../node_modules/.pnpm/@types+bcrypt@5.0.2/node_modules/@types/bcrypt", "link": true}, "node_modules/@types/cookie-parser": {"resolved": "../node_modules/.pnpm/@types+cookie-parser@1.4.7/node_modules/@types/cookie-parser", "link": true}, "node_modules/@types/cors": {"resolved": "../node_modules/.pnpm/@types+cors@2.8.17/node_modules/@types/cors", "link": true}, "node_modules/@types/express": {"resolved": "../node_modules/.pnpm/@types+express@4.17.21/node_modules/@types/express", "link": true}, "node_modules/@types/jest": {"resolved": "../node_modules/.pnpm/@types+jest@29.5.12/node_modules/@types/jest", "link": true}, "node_modules/@types/jsonwebtoken": {"resolved": "../node_modules/.pnpm/@types+jsonwebtoken@8.5.9/node_modules/@types/jsonwebtoken", "link": true}, "node_modules/@types/lodash": {"resolved": "../node_modules/.pnpm/@types+lodash@4.17.7/node_modules/@types/lodash", "link": true}, "node_modules/@types/pg": {"resolved": "../node_modules/.pnpm/@types+pg@8.11.10/node_modules/@types/pg", "link": true}, "node_modules/@types/swagger-jsdoc": {"resolved": "../node_modules/.pnpm/@types+swagger-jsdoc@6.0.4/node_modules/@types/swagger-jsdoc", "link": true}, "node_modules/@types/swagger-ui-express": {"resolved": "../node_modules/.pnpm/@types+swagger-ui-express@4.1.6/node_modules/@types/swagger-ui-express", "link": true}, "node_modules/@types/uuid": {"resolved": "../node_modules/.pnpm/@types+uuid@9.0.8/node_modules/@types/uuid", "link": true}, "node_modules/@typescript-eslint/eslint-plugin": {"resolved": "../node_modules/.pnpm/@typescript-eslint+eslint-plugin@7.18.0_@typescript-eslint+parser@7.18.0_eslint@8.57.0_typesc_d25aaf2yjh6wdbb4ovryjlos5q/node_modules/@typescript-eslint/eslint-plugin", "link": true}, "node_modules/@typescript-eslint/parser": {"resolved": "../node_modules/.pnpm/@typescript-eslint+parser@7.18.0_eslint@8.57.0_typescript@5.5.4/node_modules/@typescript-eslint/parser", "link": true}, "node_modules/ajv": {"resolved": "../node_modules/.pnpm/ajv@8.17.1/node_modules/ajv", "link": true}, "node_modules/ajv-formats": {"resolved": "../node_modules/.pnpm/ajv-formats@3.0.1_ajv@8.17.1/node_modules/ajv-formats", "link": true}, "node_modules/bcrypt": {"resolved": "../node_modules/.pnpm/bcrypt@5.1.1/node_modules/bcrypt", "link": true}, "node_modules/concurrently": {"resolved": "../node_modules/.pnpm/concurrently@6.5.1/node_modules/concurrently", "link": true}, "node_modules/cookie-parser": {"resolved": "../node_modules/.pnpm/cookie-parser@1.4.6/node_modules/cookie-parser", "link": true}, "node_modules/cors": {"resolved": "../node_modules/.pnpm/cors@2.8.5/node_modules/cors", "link": true}, "node_modules/dotenv": {"resolved": "../node_modules/.pnpm/dotenv@16.4.5/node_modules/dotenv", "link": true}, "node_modules/dotenv-expand": {"resolved": "../node_modules/.pnpm/dotenv-expand@11.0.6/node_modules/dotenv-expand", "link": true}, "node_modules/eslint": {"resolved": "../node_modules/.pnpm/eslint@8.57.0/node_modules/eslint", "link": true}, "node_modules/eslint-config-airbnb-base": {"resolved": "../node_modules/.pnpm/eslint-config-airbnb-base@15.0.0_eslint-plugin-import@2.29.1_eslint@8.57.0/node_modules/eslint-config-airbnb-base", "link": true}, "node_modules/eslint-import-resolver-typescript": {"resolved": "../node_modules/.pnpm/eslint-import-resolver-typescript@3.6.3_@typescript-eslint+parser@7.18.0_eslint@8.57.0_typesc_ueqthu3mle6dp6rd37pv2qkc2q/node_modules/eslint-import-resolver-typescript", "link": true}, "node_modules/eslint-plugin-import": {"resolved": "../node_modules/.pnpm/eslint-plugin-import@2.29.1_@typescript-eslint+parser@7.18.0_eslint@8.57.0_typescript@5.5.4___blq45kzmd5c2feotuyvehg4ffy/node_modules/eslint-plugin-import", "link": true}, "node_modules/express": {"resolved": "../node_modules/.pnpm/express@4.19.2/node_modules/express", "link": true}, "node_modules/express-oauth2-jwt-bearer": {"resolved": "../node_modules/.pnpm/express-oauth2-jwt-bearer@1.6.0/node_modules/express-oauth2-jwt-bearer", "link": true}, "node_modules/graphql": {"resolved": "../node_modules/.pnpm/graphql@16.9.0/node_modules/graphql", "link": true}, "node_modules/hasura-cli": {"resolved": "../node_modules/.pnpm/hasura-cli@2.36.1/node_modules/hasura-cli", "link": true}, "node_modules/husky": {"resolved": "../node_modules/.pnpm/husky@9.1.5/node_modules/husky", "link": true}, "node_modules/jest": {"resolved": "../node_modules/.pnpm/jest@29.7.0_@types+node@22.10.2_babel-plugin-macros@3.1.0_ts-node@10.9.2_@types+node@22.10.2_typescript@5.5.4_/node_modules/jest", "link": true}, "node_modules/jsonwebtoken": {"resolved": "../node_modules/.pnpm/jsonwebtoken@9.0.2/node_modules/jsonwebtoken", "link": true}, "node_modules/knex": {"resolved": "../node_modules/.pnpm/knex@3.1.0_pg@8.12.0/node_modules/knex", "link": true}, "node_modules/lodash": {"resolved": "../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash", "link": true}, "node_modules/nodemon": {"resolved": "../node_modules/.pnpm/nodemon@3.1.4/node_modules/nodemon", "link": true}, "node_modules/pg": {"resolved": "../node_modules/.pnpm/pg@8.12.0/node_modules/pg", "link": true}, "node_modules/pino": {"resolved": "../node_modules/.pnpm/pino@9.3.2/node_modules/pino", "link": true}, "node_modules/pino-pretty": {"resolved": "../node_modules/.pnpm/pino-pretty@11.2.2/node_modules/pino-pretty", "link": true}, "node_modules/swagger-jsdoc": {"resolved": "../node_modules/.pnpm/swagger-jsdoc@6.2.8_openapi-types@12.1.3/node_modules/swagger-jsdoc", "link": true}, "node_modules/swagger-ui-express": {"resolved": "../node_modules/.pnpm/swagger-ui-express@5.0.1_express@4.19.2/node_modules/swagger-ui-express", "link": true}, "node_modules/ts-jest": {"resolved": "../node_modules/.pnpm/ts-jest@29.2.5_@babel+core@7.25.2_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@29.7.0_ewwkr2bge6y26cvjwby3vgtj7i/node_modules/ts-jest", "link": true}, "node_modules/ts-node": {"resolved": "../node_modules/.pnpm/ts-node@10.9.2_@types+node@22.10.2_typescript@5.5.4/node_modules/ts-node", "link": true}, "node_modules/tsc-alias": {"resolved": "../node_modules/.pnpm/tsc-alias@1.8.10/node_modules/tsc-alias", "link": true}, "node_modules/tsconfig-paths": {"resolved": "../node_modules/.pnpm/tsconfig-paths@4.2.0/node_modules/tsconfig-paths", "link": true}, "node_modules/typescript": {"resolved": "../node_modules/.pnpm/typescript@5.5.4/node_modules/typescript", "link": true}, "node_modules/uuid": {"resolved": "../node_modules/.pnpm/uuid@10.0.0/node_modules/uuid", "link": true}}}