table:
  name: company
  schema: public
configuration:
  column_config:
    country:
      custom_name: country
    created_at:
      custom_name: createdAt
    description:
      custom_name: description
    id:
      custom_name: id
    name:
      custom_name: name
    tributary_id:
      custom_name: tributaryId
    updated_at:
      custom_name: updatedAt
    verified_at:
      custom_name: verifiedAt
  custom_column_names:
    country: country
    created_at: createdAt
    description: description
    id: id
    name: name
    tributary_id: tributaryId
    updated_at: updatedAt
    verified_at: verifiedAt
  custom_root_fields: {}
array_relationships:
  - name: attributes
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: attribute
          schema: public
  - name: catalog_discounts
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: catalog_discount
          schema: public
  - name: catalogs
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: catalog
          schema: public
  - name: clients
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: client
          schema: public
  - name: clientsByClientCompanyId
    using:
      foreign_key_constraint_on:
        column: client_company_id
        table:
          name: client
          schema: public
  - name: inventories
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: inventory
          schema: public
  - name: provider_of
    using:
      foreign_key_constraint_on:
        column: provider_company_id
        table:
          name: provider
          schema: public
  - name: store_discounts
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: store_discount
          schema: public
  - name: user_companies
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: user_company
          schema: public
object_relationships:
  - name: country_relation
    using:
      foreign_key_constraint_on: country
select_permissions:
  - role: read:hasura
    permission:
      columns:
        - country
        - created_at
        - description
        - id
        - name
        - tributary_id
        - updated_at
        - verified_at
      filter: {}
    comment: ""
  - role: read_user
    permission:
      columns:
        - country
        - created_at
        - description
        - id
        - name
        - tributary_id
        - updated_at
        - verified_at
      filter:
        _or:
          - id:
              _eq: X-Hasura-Company-Id
          - clients:
              client_company_id:
                _eq: X-Hasura-Company-Id
      allow_aggregations: true
    comment: ""
